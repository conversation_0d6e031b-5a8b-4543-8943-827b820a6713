# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [1.11.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.10.0...v1.11.0) (2025-08-15)

### Features

- 调整机构管理抽成比例设置展示样式 ([a403f5d](https://github.com/pure-admin/vue-pure-admin/commit/a403f5dc07d612bd0b6bbffce9c1ea8602261429))
- 订单详情新增原价展示 ([8f8680d](https://github.com/pure-admin/vue-pure-admin/commit/8f8680de7bf6213f8b2d6c5572e6f82ed9e2e33f))
- 订单详情新增子订单原价展示 ([8389837](https://github.com/pure-admin/vue-pure-admin/commit/8389837ce9be2bf3c3061142b6f192f78c1d126f))
- 评语库操作增加操作日志记录功能，完善新增、编辑和删除操作的日志参数 ([8bd88a9](https://github.com/pure-admin/vue-pure-admin/commit/8bd88a9fe25dacaa27008f00ae78af24d6de8fcb))
- 添加促销价相关功能，包括促销价显示、促销规则设置及数据回显逻辑 ([5f7c9ed](https://github.com/pure-admin/vue-pure-admin/commit/5f7c9edbae9d9d9101ffd6f48ef22817bb2cc6c1))
- 添加原价列显示功能 ([93dbd64](https://github.com/pure-admin/vue-pure-admin/commit/93dbd64b228252dba9d9db20b046d61fe8993ba5))
- 新增改期相关审核和展示 ([dc95553](https://github.com/pure-admin/vue-pure-admin/commit/dc955536a6547a5224eb765191c217e3ddbe654c))
- 重构地区选择功能，使用本地JSON数据替代API调用，优化地址回显逻辑 ([2326264](https://github.com/pure-admin/vue-pure-admin/commit/2326264bf52c4ef2b71a98473ef776d158e33f2b))

### Bug Fixes

- 调整课程详情页封面图片展示样式，优化图片尺寸和位置 ([6200b92](https://github.com/pure-admin/vue-pure-admin/commit/6200b921709e2ee735a32e83bf4f28b972989c85))
- 将表单标签从"课程名"改为"课期名称" ([fa842ee](https://github.com/pure-admin/vue-pure-admin/commit/fa842eeb8bf49cb87c8bb5899c6a35e66473b037))
- 局端刷新退出登录问题处理 ([6dbfe92](https://github.com/pure-admin/vue-pure-admin/commit/6dbfe9286f7c12a8afd949a0eb87704f8067728a))
- 课期图片有拉伸 ([0566e79](https://github.com/pure-admin/vue-pure-admin/commit/0566e7936456c2423d34808db0c3143f0164eb3a))
- 课期详情视频第一次预览加载未显示 ([eb0961b](https://github.com/pure-admin/vue-pure-admin/commit/eb0961b98cd5af8877a5978220f186328d197bb3))
- 课堂追踪视频显示 ([fc670b0](https://github.com/pure-admin/vue-pure-admin/commit/fc670b0931059d5bf3d5b4d9aaa75d275177d77f))
- 评价管理关联课期调整 ([35f78ba](https://github.com/pure-admin/vue-pure-admin/commit/35f78baee7f3654a214a9dc232d8f784285878a3))
- 评价管理课期名鼠标移入字体颜色变蓝且可点击 ([d0340fe](https://github.com/pure-admin/vue-pure-admin/commit/d0340fea05d5a4b97867c3a7087140486c88d7e9))
- 申诉管理表格中将"课程名称"列名修改为"课期名称 ([df71d0f](https://github.com/pure-admin/vue-pure-admin/commit/df71d0fa468c0772581f11d38e071811e4fba7c6))
- 申诉管理优化订单和课程名称展示为可点击链接，移除冗余操作按钮并调整操作列宽度 ([5011b32](https://github.com/pure-admin/vue-pure-admin/commit/5011b32f571bcf6355b9ceda7cc40e5921d7093d))
- 限制保险费输入最大值不超过9999.99 ([7fce197](https://github.com/pure-admin/vue-pure-admin/commit/7fce197fcac1c4461d9006752a4d88a80b970cd5))
- 行程安排显示调整 ([822dcf3](https://github.com/pure-admin/vue-pure-admin/commit/822dcf345cbdef8bd5f02c0b5bafbd5a0d9e474b))
- 修改课程名和课程期号的标签文本，优化退款确认逻辑，移除冗余的二次确认步骤 ([51f76c6](https://github.com/pure-admin/vue-pure-admin/commit/51f76c6c487e92e2a0829997b496db74f3a0258f))
- 优化财务管理页面路由和样式，调整订单详情链接及课程名展示 ([9d1ef5c](https://github.com/pure-admin/vue-pure-admin/commit/9d1ef5c6afacc9fc43a75b666e1f70536ca36b77))
- 优化课期详情视频展示 ([2556807](https://github.com/pure-admin/vue-pure-admin/commit/2556807aa5f0de95d5e684c004c92b947969e92b))
- 优化申诉管理表格样式，移除边框并设置表头背景色为白色 ([c3ab9fd](https://github.com/pure-admin/vue-pure-admin/commit/c3ab9fdec8b62abd1b75efefe4ace13d98818d04))
- 优化申诉管理处理不了的问题 ([6e75638](https://github.com/pure-admin/vue-pure-admin/commit/6e7563898231a8ce739447a440e2df2a5aa74d9f))

## [1.10.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.9.0...v1.10.0) (2025-08-09)

### Features

- 财务管理订单管理新增课期跳转 ([7caa968](https://github.com/pure-admin/vue-pure-admin/commit/7caa968919fa30cd5b564019b0f0df0873851b35))
- 财务管理静态+机构管理设置抽成静态 ([7a2e724](https://github.com/pure-admin/vue-pure-admin/commit/7a2e724e65d8fc2aa51b925184c07ef1180c93cd))
- 更新价格设置功能，调整接口路径，新增价格设置数据结构及展示样式 ([2ad4408](https://github.com/pure-admin/vue-pure-admin/commit/2ad44087a39ebe77f77dc13ce8ea0fdfafd3e5ff))
- 机构详情页新增归属地信息展示及地区查询功能 ([371dfe4](https://github.com/pure-admin/vue-pure-admin/commit/371dfe49b9b93a341548be07ffdb6a41cdebfdb5))
- 课程审核详情添加下架申请理由展示及课期审核数据改变课期列表页对应数据更新 ([62b01cf](https://github.com/pure-admin/vue-pure-admin/commit/62b01cf63751e39ef0a5a2350461e340eee44255))
- 课程审核详情页费用设置模块添加局端不显示 ([3098bab](https://github.com/pure-admin/vue-pure-admin/commit/3098bab274c92d508530ea88158122b542ec0ec4))
- 课程审核新增转交局端功能及状态显示优化 ([1002027](https://github.com/pure-admin/vue-pure-admin/commit/1002027040b170a9ed3da908c8b57efb39289a25))
- 课程审批完毕新增刷新功能 ([7fb08d6](https://github.com/pure-admin/vue-pure-admin/commit/7fb08d6a0102513704077e4496bd01cb4acf3bd5))
- 课程详情页面新增课程简介展示及样式调整 ([bab834a](https://github.com/pure-admin/vue-pure-admin/commit/bab834a04148fb425778b8cea8924a927dbc8b6d))
- 课期详情视频展示 ([1bd7874](https://github.com/pure-admin/vue-pure-admin/commit/1bd78749c0dedab9c3c4942efcda9613df10168b))
- 申诉管理功能重构，对接真实API接口，优化处理流程和UI展示 ([4c9d13b](https://github.com/pure-admin/vue-pure-admin/commit/4c9d13bbf5693771207f0221d146eb95ff742f3a))
- 申诉管理细节调整 ([a2056d0](https://github.com/pure-admin/vue-pure-admin/commit/a2056d05a439bad12dac16e0defc256708dab188))
- 申诉管理新增订单和课程详情跳转功能，优化批量处理逻辑和预设意见模板 ([ed760e8](https://github.com/pure-admin/vue-pure-admin/commit/ed760e84e25221e33e0f6763c89fdee0ebac4c67))
- 申诉管理优化按钮显示逻辑，根据tab状态动态显示处理按钮或状态，并添加最大宽度限制 ([0d60909](https://github.com/pure-admin/vue-pure-admin/commit/0d609091c6cba90ac2921a91e9c760cafcf0bf7b))
- 为订单管理页面新增订单状态和退款状态的颜色映射功能 ([97b4996](https://github.com/pure-admin/vue-pure-admin/commit/97b4996c1409f90b42de285a2567eb987827ecbe))
- 新增服务费比例编辑功能，优化课程审核页面费用相关字段处理 ([4c17c17](https://github.com/pure-admin/vue-pure-admin/commit/4c17c174747b29f815813ccb8e54193330595f51))
- 新增批量处理退单功能，包括批量确认和驳回退单的弹窗及相关逻辑 ([d86a5eb](https://github.com/pure-admin/vue-pure-admin/commit/d86a5eb91204a6aaacb5ac91ea339b1c02346082))
- 新增申诉管理静态页面 ([bf7404e](https://github.com/pure-admin/vue-pure-admin/commit/bf7404edf25d7cb387830fbe2293137d9bf3a95c))
- 新增审核类型处理逻辑，优化费用字段回显及计算方式 ([e56fb63](https://github.com/pure-admin/vue-pure-admin/commit/e56fb6342c5603b5d9bce99bfdf4123720ff87fe))
- 新增退款管理功能，包括主订单和子订单的确认与驳回接口，优化财务管理页面，调整表格显示和参数处理 ([f0be4aa](https://github.com/pure-admin/vue-pure-admin/commit/f0be4aa5b8fda9f84c43d530cb07837fe9b0887f))
- 修改局端接口 ([1aee164](https://github.com/pure-admin/vue-pure-admin/commit/1aee164de1ceea92e5845f88a1b80b32ff61ae9c))
- 学生情况展示材料费购买人数及是否购买材料费 ([b9c3158](https://github.com/pure-admin/vue-pure-admin/commit/b9c315869157c6f46ad404445124ec2d80923977))
- 优化订单详情组件，新增费用明细和购买金额字段，添加费用明细处理和购买金额计算功能 ([6b9d7fd](https://github.com/pure-admin/vue-pure-admin/commit/6b9d7fd5ba7b22cd7e8a67ec072b3c10b5127f75))
- 在财务管理页面新增订单号和课期名称字段，优化表格展示 ([93b9d8c](https://github.com/pure-admin/vue-pure-admin/commit/93b9d8c36f99cb67d86490548cbc4c93f3d22b08))

### Bug Fixes

- 调整机构详情表单字段顺序，移除重复的一句话简介字段 ([d8824e8](https://github.com/pure-admin/vue-pure-admin/commit/d8824e86d9ca8779c919d78097f427781ca6d4f7))
- 调整机构详情表单字段顺序位置 ([f415075](https://github.com/pure-admin/vue-pure-admin/commit/f415075d3084d202f5c795dcb5df6c046ad44e00))
- 价格设置、作业、报告页面调整 ([f280c2b](https://github.com/pure-admin/vue-pure-admin/commit/f280c2b4983419e9133e6fbb3384b1811f144f1b))
- 评价管理关联课程 ([a4b16c7](https://github.com/pure-admin/vue-pure-admin/commit/a4b16c732c00877a1f89382bc2d5908b4ed341dd))
- 评价管理回复审核ui异常 ([d7723d2](https://github.com/pure-admin/vue-pure-admin/commit/d7723d2fa67d17935dbb19779ec50072b8b22737))
- 申诉日志添加，课程审核bug处理 ([67d6a23](https://github.com/pure-admin/vue-pure-admin/commit/67d6a23ab2f12201fb8765adc007facf67f7b3f0))
- 限制服务费比例为100%，并根据比例调整服务费计算逻辑 ([484813e](https://github.com/pure-admin/vue-pure-admin/commit/484813e8a3950b5576cf017974a4a56620bbea0a))
- 修复财务管理表格高度异常 ([a070729](https://github.com/pure-admin/vue-pure-admin/commit/a07072934ade67c471ff9a8cfb7838e3dc38bf61))
- 修复订单管理财务管理跳转面包屑异常 ([3457c95](https://github.com/pure-admin/vue-pure-admin/commit/3457c9567c6867354d22af68c389041af949f531))
- 学生情况加上人数上限及材料费用 ([68f94a4](https://github.com/pure-admin/vue-pure-admin/commit/68f94a4b262bd2105c0dbca426bbbd179f1a99aa))
- 移除课程审核页面激活钩子并添加局端管理员类型参数 ([fa214ed](https://github.com/pure-admin/vue-pure-admin/commit/fa214ed2ad20809e0fced5092620e62cb912c6a6))
- 优化财务管理页面，移除不必要的滚动条组件，调整样式以改善表格显示效果 ([6b4051a](https://github.com/pure-admin/vue-pure-admin/commit/6b4051a47a4e08c99ae1148c844e8e2f90cec2f6))
- 优化课程知识页面样式和表格布局，调整申诉管理按钮样式和操作列宽度 ([387b1dd](https://github.com/pure-admin/vue-pure-admin/commit/387b1dd7f78af8d0930b4a730b4d391231f42d43))
- 优化评价管理驳回原因及驳回显示 ([5ac52af](https://github.com/pure-admin/vue-pure-admin/commit/5ac52af1b9dba54d85f5352142b0941691402717))

## [1.9.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.8.0...v1.9.0) (2025-08-02)

### Features

- 参与课程静态 ([88ccc71](https://github.com/pure-admin/vue-pure-admin/commit/88ccc717d44bf22412a656e7b0482cc95c3bdeb5))
- 订单退款新增退款理由 ([8efe69a](https://github.com/pure-admin/vue-pure-admin/commit/8efe69a57a929978192c8f5e7ceb2007d9c329d7))
- 发现富文本编辑器特殊调整 ([ea8b3fc](https://github.com/pure-admin/vue-pure-admin/commit/ea8b3fc9d43b695e2b640516b819120681db1a64))
- 将课期知识点从课程介绍中分离并使用独立组件展示 ([6666531](https://github.com/pure-admin/vue-pure-admin/commit/6666531cea348afaddde77fa2743db0404604ff7))
- 将课期知识点功能拆分为独立组件并优化展示 ([d7998e4](https://github.com/pure-admin/vue-pure-admin/commit/d7998e4f7cb61a504f91d5e04b12df63d939b14b))
- 课程详情新增课程年龄段展示 ([f0dfb7f](https://github.com/pure-admin/vue-pure-admin/commit/f0dfb7fa6c94a7c613eb74f91d06cf6710fa8ee2))
- 评价管理静态页面 ([10f4527](https://github.com/pure-admin/vue-pure-admin/commit/10f4527ebc9d92dc27dca41464eb8513b1e44371))
- 评价管理列表页 ([f7880a5](https://github.com/pure-admin/vue-pure-admin/commit/f7880a576019d24e15d9d36c789aaab77761e7ee))
- 评价管理审核 ([b46ee9b](https://github.com/pure-admin/vue-pure-admin/commit/b46ee9b123e09b0a2ad500cde2a0b8addad20f0b))
- 日志新增操作描述搜索 ([c47d100](https://github.com/pure-admin/vue-pure-admin/commit/c47d10076567aa361b587291d315f7ab017d96c2))
- 日志新增评价管理课期驳回新增选择理由 ([6edf7f3](https://github.com/pure-admin/vue-pure-admin/commit/6edf7f398b2b2a566889404658e6a3ae8025c883))
- 师资库、师资审核下拉栏使用接口返回数据 ([f952787](https://github.com/pure-admin/vue-pure-admin/commit/f9527875a0b7b4107614a9371e15d4a8ab24c944))
- 师资库表单+搜索栏+冻结 ([7ae5645](https://github.com/pure-admin/vue-pure-admin/commit/7ae56452c4e5062a35e92adffae00e3f3f651e43))
- 师资库等侧边栏展示 ([3f0d4cc](https://github.com/pure-admin/vue-pure-admin/commit/3f0d4cc97dcfd3615f9a3cccc0f6fd854dedd0ae))
- 师资库批量导入 ([5b77b94](https://github.com/pure-admin/vue-pure-admin/commit/5b77b94bf20e948be4f32dd80d3ea6efa695086c))
- 师资库师资详情和部分日志 ([d9ccd62](https://github.com/pure-admin/vue-pure-admin/commit/d9ccd6253993fbdee77813d5b1048d5f1a48a5d1))
- 师资库头像上传相关调整 ([9721aee](https://github.com/pure-admin/vue-pure-admin/commit/9721aee979ccff311f21d4346ae5add5e903ac11))
- 师资库新增和编辑 ([7d2f1e4](https://github.com/pure-admin/vue-pure-admin/commit/7d2f1e4204aae2a8587577b80caf1b0a43c1c6d2))
- 师资审核 ([f09c12c](https://github.com/pure-admin/vue-pure-admin/commit/f09c12c958d922ccf38211c5f13b7500bfa84019))
- 师资审核表单+搜索 ([f3a50ce](https://github.com/pure-admin/vue-pure-admin/commit/f3a50ce98943c562267b8d12701b81d6808e26cc))
- 师资审核添加日志 ([73ce28f](https://github.com/pure-admin/vue-pure-admin/commit/73ce28fe8369b9049e6c051770f60981a1bd4548))
- 师资审核新增状态详情页 ([668c78d](https://github.com/pure-admin/vue-pure-admin/commit/668c78d2a7bd4da2211d6b1712de5d2464029fc7))
- 师资审核修改状态页面展示 ([00c81e4](https://github.com/pure-admin/vue-pure-admin/commit/00c81e42f9426406aff5fe354c1a44a9796d1436))
- 添加课程和审核状态的颜色映射，并优化课程详情页面的表格展示 ([e4491b7](https://github.com/pure-admin/vue-pure-admin/commit/e4491b70ddb54d387d581fd1e7f176b3b9ae49c5))
- 新增课程市场模块及相关页面 ([0986545](https://github.com/pure-admin/vue-pure-admin/commit/098654572d1c3812d417966d30a4b95c535c013f))
- 新增评语库功能，包括路由配置、API接口和页面实现 ([bd3d4aa](https://github.com/pure-admin/vue-pure-admin/commit/bd3d4aac22fb1671986526fe86265fe23ba5ac9f))
- 新增新闻管理板块 ([0273aab](https://github.com/pure-admin/vue-pure-admin/commit/0273aab1bcf6f04377c69b42c4fd98ce60987357))
- 新增知识库模块及相关接口和页面功能 ([0985c09](https://github.com/pure-admin/vue-pure-admin/commit/0985c092747108c1989903cfd8dfc88832f88b1e))
- 引入平台信息展示 ([50dfb39](https://github.com/pure-admin/vue-pure-admin/commit/50dfb396a5c5a45315cbb6e92227623b974d300a))
- 隐藏师资库师资审核板块 ([fa9bd2f](https://github.com/pure-admin/vue-pure-admin/commit/fa9bd2f3be385e53c72e14eaf843303dd97237f3))
- 优化基地详情页图片展示样式和文本颜色 ([242a139](https://github.com/pure-admin/vue-pure-admin/commit/242a139438ce3dbec2d487fe162293a92b7eed1f))
- 优化课程详情页面布局 ([650d57a](https://github.com/pure-admin/vue-pure-admin/commit/650d57ab8e758a45bfdd1c54752271ad2d2c9dda))
- 专家评价静态 ([811054f](https://github.com/pure-admin/vue-pure-admin/commit/811054f8ed7951b1f8347ea95b3948fdf1c380f8))

### Bug Fixes

- 调整评语库列表默认分页大小从10改为15 ([52c307c](https://github.com/pure-admin/vue-pure-admin/commit/52c307cec30ed2e89d36c3140cc580a98e8df20c))
- 基地更名为实践点，基地详情字段增加 ([0190d02](https://github.com/pure-admin/vue-pure-admin/commit/0190d02092fe034a749e14ba12398c10bc11b2f3))
- 课程标签改为课程亮点标签 ([c565667](https://github.com/pure-admin/vue-pure-admin/commit/c565667750a8cadf96ac8982226b4eebbf1b0244))
- 课程评价新增评价状态和审核操作 ([5a847b3](https://github.com/pure-admin/vue-pure-admin/commit/5a847b3393e7e6ae60f1372d1e41599dfe37307e))
- 课程取消定制 ([9f939f8](https://github.com/pure-admin/vue-pure-admin/commit/9f939f85d4d4bcf94c12db25401f261be167bc2b))
- 课程详情页面细节调整 ([ab26782](https://github.com/pure-admin/vue-pure-admin/commit/ab267820bc33298f57b85d507cd9ebf898c456eb))
- 评价管理页面优化 ([a633ebf](https://github.com/pure-admin/vue-pure-admin/commit/a633ebfcc6499c383b1e8a420e8fd7fcccb28c03))
- 屏蔽课程市场 ([7c7d99b](https://github.com/pure-admin/vue-pure-admin/commit/7c7d99b4e406c28d808c46af75e51983b0aa98bb))
- 师资板块自测修复 ([de5e39e](https://github.com/pure-admin/vue-pure-admin/commit/de5e39e59ada4627e399462a7e1d09b8dd832940))
- 统一将"基地"更名为"实践点"，更新相关页面和组件 ([5551df4](https://github.com/pure-admin/vue-pure-admin/commit/5551df406aca31753065536107776290625c1989))
- 修改师资库详情中的身份证号标签为证件号 ([f4618f7](https://github.com/pure-admin/vue-pure-admin/commit/f4618f79703eb337de968136dd1e0d19820ba14f))
- 移除评语库列表调试日志并优化页码计算注释 ([8096512](https://github.com/pure-admin/vue-pure-admin/commit/809651267b394e2d1668270eb9aa7d23f0656674))
- 优化课程市场页面样式 ([119a1e0](https://github.com/pure-admin/vue-pure-admin/commit/119a1e069110b66283eda3694d7cfec4c294b0ee))
- 装备说明改为材料说明 ([b349622](https://github.com/pure-admin/vue-pure-admin/commit/b349622ddbce962e4cfb5c48ecf88a5136262d2f))
- 作业改为实践感悟 ([4f0a06c](https://github.com/pure-admin/vue-pure-admin/commit/4f0a06c3c2d47153f997a70ca4da36202106558a))

## [1.8.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.7.1...v1.8.0) (2025-07-25)

### Features

- 发现富文本字体相关特殊处理 ([03d329f](https://github.com/pure-admin/vue-pure-admin/commit/03d329f67e3e76f7808ce7acc65b561e14e785b8))
- 添加官方号头像和名称字段，优化文件上传处理逻辑 ([fe19a5f](https://github.com/pure-admin/vue-pure-admin/commit/fe19a5f97597fe504943a46d7eea572393e938cb))

### Bug Fixes

- 机构表单增加必填验证，优化校验逻辑， ([9752041](https://github.com/pure-admin/vue-pure-admin/commit/97520414dd8d917975284b7a80c348cf3dda1c07))
- 机构基础信息成立时间只能选择今天及之前的日期 ([2990217](https://github.com/pure-admin/vue-pure-admin/commit/29902174dbc846b0cc9b8b1518cdc6450cd77f0f))
- 课堂跟踪时间调整 ([692996e](https://github.com/pure-admin/vue-pure-admin/commit/692996e045328585e365ea0cc3d20be12d9f1258))
- 平台账号和局端账号输入框中间去空及数字输入禁用输入文字 ([f652cf5](https://github.com/pure-admin/vue-pure-admin/commit/f652cf5aac76671f0142d96651daf21debeca244))
- 修复机构基础信息中管理员账号截取逻辑 ([a79339b](https://github.com/pure-admin/vue-pure-admin/commit/a79339b92fbd8c63004594fb6dd43ba660dbe3b4))
- 移除版本检测逻辑，简化应用初始化过程 ([f1d5e23](https://github.com/pure-admin/vue-pure-admin/commit/f1d5e23478d0782911be7a91db521d00215eae17))
- 优化机构基础信息移除账号截取逻辑 ([a718b82](https://github.com/pure-admin/vue-pure-admin/commit/a718b82598d8aa6523e306c54375ae62bc60b551))

### [1.7.1](https://github.com/pure-admin/vue-pure-admin/compare/v1.7.0...v1.7.1) (2025-07-10)

### Bug Fixes

- 数据分析及图片更新调整 ([a07a5e3](https://github.com/pure-admin/vue-pure-admin/commit/a07a5e333e82f4e0d7523747c28e27e67077ddb8))
- 数据分析跳转 ([3a51f71](https://github.com/pure-admin/vue-pure-admin/commit/3a51f714a47f81ea02703bafd7af6cd596f20069))
- 修改登录页面logo样式 ([ca9c9ed](https://github.com/pure-admin/vue-pure-admin/commit/ca9c9edb1ef00aaa373dff015a84711e11025548))
- 修改登录页面logo展示变为响应式 ([3bc6a43](https://github.com/pure-admin/vue-pure-admin/commit/3bc6a43cee802e373324366326ddec7c4eb6a2ed))
- 优化仪表板点击处理逻辑，修复跳转链接生成 ([e9be22b](https://github.com/pure-admin/vue-pure-admin/commit/e9be22b0c9bb8140e69bdac7e16cfb0b9096b747))

## [1.7.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.6.0...v1.7.0) (2025-07-09)

### Features

- 机构表单编辑增加验证码功能及自动密码生成功能 ([368e977](https://github.com/pure-admin/vue-pure-admin/commit/368e9774076c81856e74b8123195011baac4dca6))
- 机构表单增加文件重复上传校验 ([af3d523](https://github.com/pure-admin/vue-pure-admin/commit/af3d5235593107859529b3a2474e2b3ff78f2481))
- 数据分析模块 ([4603ce4](https://github.com/pure-admin/vue-pure-admin/commit/4603ce4934c06bb1b29c154f80039cc23d524fb9))
- 增加角色名称和备注的输入校验，禁止角色名称输入空格 ([2deeec0](https://github.com/pure-admin/vue-pure-admin/commit/2deeec0a1a7b9cb07ceed02ad2cb00580f92848c))

### Bug Fixes

- 机构表单电话验证，地址选择器只读属性 ([8c463ae](https://github.com/pure-admin/vue-pure-admin/commit/8c463ae3f86ac7f1ad7f4a8c95676f3c975b641a))
- 领队讲师编辑信息报错文案放行内 ([fb340e5](https://github.com/pure-admin/vue-pure-admin/commit/fb340e5c495937fe32716eb0ab5c0b19b1378b7b))
- 平台账号邮箱校验和报错文案行内显示 ([a7d2af6](https://github.com/pure-admin/vue-pure-admin/commit/a7d2af6c11d0a1e4c5325bf7ad714de50beb8406))
- 评价评分优化 ([e0e2bd6](https://github.com/pure-admin/vue-pure-admin/commit/e0e2bd6449a3570a152938b6f07bdffccda49a57))
- 日志管理搜索操作人去空格 ([b27002e](https://github.com/pure-admin/vue-pure-admin/commit/b27002e98118e72918619c25d11636bb0c89e174))
- 修正机构表单中营业执照图片上传的最佳尺寸说明 ([4fb153d](https://github.com/pure-admin/vue-pure-admin/commit/4fb153d017e2b44b3cf3336ed19e72bd1f2eff09))
- 在身份证号码验证中添加空值处理，确保表单验证逻辑的完整性 ([7c05bda](https://github.com/pure-admin/vue-pure-admin/commit/7c05bda01ca0ecc6b2b549ce8cf0d579562ebf48))
- 重置订单管理表单时初始化字段，确保表单状态一致 ([6e55ddd](https://github.com/pure-admin/vue-pure-admin/commit/6e55ddd59e621f744fcad55069e08b0bd0f3556f))
- 专家库列表优化 ([e306a4f](https://github.com/pure-admin/vue-pure-admin/commit/e306a4fc58fe85cd0f897a70daa69368722b80ab))
- 专家库删除文案修改 ([3874200](https://github.com/pure-admin/vue-pure-admin/commit/38742001e0eb52fe84e00dfee1d168d7a3d2f6d2))

## [1.6.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.5.0...v1.6.0) (2025-07-03)

### Features

- 机构表单增加验证码功能，优化地图定位体验 ([27aee54](https://github.com/pure-admin/vue-pure-admin/commit/27aee540f2b483ce4ea0ab9e9ac855e38812d6a3))
- 机构表单增加字段长度限制及文件上传校验 ([474bfb6](https://github.com/pure-admin/vue-pure-admin/commit/474bfb6e701995508f6ad528f19926e953ff4ca7))

### Bug Fixes

- 调整机构简介最大长度限制为200字 ([a400756](https://github.com/pure-admin/vue-pure-admin/commit/a400756b9bd3d494097673ddbd7c366c68beca42))
- 更新白名单以优化路由匹配，增加更多路径支持 ([205c909](https://github.com/pure-admin/vue-pure-admin/commit/205c909a695b7d9e825de9a89efd8d27a66a0831))
- 更新微信绑定请求处理逻辑，增加会话存储记录以防止重复处理 ([137db2b](https://github.com/pure-admin/vue-pure-admin/commit/137db2ba53478d8e1bf3765388d433124b8cd5a2))
- 归档面包屑调整 ([4a936ad](https://github.com/pure-admin/vue-pure-admin/commit/4a936ad3082106b0511cf4a420263568e7e83820))
- 恢复线上环境删除console的代码以确保生产环境的清洁 ([c450494](https://github.com/pure-admin/vue-pure-admin/commit/c4504940bd437e1b48019277b5cc441557075b2f))
- 回退面包屑调整 ([af63066](https://github.com/pure-admin/vue-pure-admin/commit/af6306651c2f4d3f82f0579ce2d2a3dc257a57a1))
- 机构表单调整间距 ([8b78942](https://github.com/pure-admin/vue-pure-admin/commit/8b7894203835d29ebdbafa0c4faf58f072e6ee62))
- 机构管理财务列表title滚动消失 ([2d5f9e1](https://github.com/pure-admin/vue-pure-admin/commit/2d5f9e1e988cfa4a768dacc80e309749272d26ae))
- 机构管理图片预览 ([b0a97aa](https://github.com/pure-admin/vue-pure-admin/commit/b0a97aae453f9d59974afd90d82a69fa97b1e990))
- 机构基础信息bug处理 ([817342e](https://github.com/pure-admin/vue-pure-admin/commit/817342ed906210c8cf03941433160d644aa6dc34))
- 机构基础信息bug处理 ([2c4d554](https://github.com/pure-admin/vue-pure-admin/commit/2c4d5546ca75fc9ff30eda46748140534e432222))
- 简化二维码登录错误处理，移除多种特殊状态码的处理，统一错误提示方式 ([be684e1](https://github.com/pure-admin/vue-pure-admin/commit/be684e1f2a8997aa61544f52f6f489d54b0b95ae))
- 课程审核调整 ([8f81020](https://github.com/pure-admin/vue-pure-admin/commit/8f81020e855369c34118a7d4bc9a1fde6ff73081))
- 课程审核列表UI调整 ([4cca413](https://github.com/pure-admin/vue-pure-admin/commit/4cca413d91490557ed2db6f68fe3bbbbc9566eaa))
- 课期审核相关局端接口分离及局端日志 ([0781fb1](https://github.com/pure-admin/vue-pure-admin/commit/0781fb1179ec2c7ec6f2c3361fea8ba67e161968))
- 去掉账号管理修改手机号验证码 ([fd573be](https://github.com/pure-admin/vue-pure-admin/commit/fd573be0b8c0c2d8a862cc474ffce911af40a207))
- 确保在登录流程中保持平台管理员角色选择，优化二维码登录错误处理 ([a465548](https://github.com/pure-admin/vue-pure-admin/commit/a46554890028ed2a19618bbb35ac3d914933db13))
- 上传图片样式处理 ([9b5ba11](https://github.com/pure-admin/vue-pure-admin/commit/9b5ba111f754b61fb316e6100d664070d26b7899))
- 添加二维码登录时的调试信息 ([e26c60f](https://github.com/pure-admin/vue-pure-admin/commit/e26c60f8245fcbcf2d88ff4f0b3a9a377ce958de))
- 修复二维码登录时错误信息的输出方式，改为使用console.error并添加错误码 ([ebd20dc](https://github.com/pure-admin/vue-pure-admin/commit/ebd20dcbce901259172cdef836b2ca96faa4a171))
- 修复身份证填写时，切换小眼睛数据错误 ([e038df9](https://github.com/pure-admin/vue-pure-admin/commit/e038df9e1e385c128c45471c56f38207e8109d6b))
- 修复微信绑定操作日志中的引号格式，并清除URL参数以防止重复绑定 ([49c6861](https://github.com/pure-admin/vue-pure-admin/commit/49c6861a8ca0ea94da4fee869263ee7e801fac49))
- 修复修改账号身份证的时候报错 ([da4c127](https://github.com/pure-admin/vue-pure-admin/commit/da4c127259625833ec99336b4d06c22ba2697c6d))
- 修复用户信息获取方式，调整为从result.data中提取用户和token ([5eeee2e](https://github.com/pure-admin/vue-pure-admin/commit/5eeee2eb13d75e53b9b1efde38e1fbc7c3961f6a))
- 修改首页样式 ([99894ad](https://github.com/pure-admin/vue-pure-admin/commit/99894adf6c0273b5ab34103dd340872a0d6bc049))
- 修正图片上传大小单位描述 ([c3891c1](https://github.com/pure-admin/vue-pure-admin/commit/c3891c1fa71b93732e0f92b4515e1e8a7d9c0d48))
- 移除多余的调试日志，优化面包屑获取逻辑 ([714ea96](https://github.com/pure-admin/vue-pure-admin/commit/714ea962c78e7692c652eeda0d4017f4aba6ca07))
- 优化二维码登录错误处理，添加详细的错误信息记录和提示 ([6a3c2d8](https://github.com/pure-admin/vue-pure-admin/commit/6a3c2d8934feae60db0ddafb42d3531352886fac))
- 优化二维码登录错误处理逻辑 ([1c795f1](https://github.com/pure-admin/vue-pure-admin/commit/1c795f1f4e9a27893a8d13624e0a3ecd1ff14570))
- 优化二维码登录错误信息处理，使用统一的消息提示方式 ([6c20224](https://github.com/pure-admin/vue-pure-admin/commit/6c20224fff11ce396f3c18244d5a8972c0f8713d))
- 优化面包屑逻辑，增加安全性检查，改进微信状态处理提示 ([a094efb](https://github.com/pure-admin/vue-pure-admin/commit/a094efb3718f2c02c2dba6fb2cfd6672282e2d7e))
- 优化微信绑定和解绑操作，增加防抖处理，改进操作日志信息 ([7d01ed7](https://github.com/pure-admin/vue-pure-admin/commit/7d01ed7e96e8cdc5b0d1d0f90ea06f9b1189b9cc))
- 优化微信绑定请求处理逻辑，避免重复处理并记录已处理状态到会话存储 ([9d413f7](https://github.com/pure-admin/vue-pure-admin/commit/9d413f77b0d3e701d97c9bb0b4e027c39f2a740d))
- 优化微信绑定状态展示和解绑确认提示，调整样式 ([4b55ef0](https://github.com/pure-admin/vue-pure-admin/commit/4b55ef0bf3d08cc733da2ce57ea040165b4cb275))
- 优化微信解绑提示，改用对象形式显示消息；增加重定向测试函数 ([8406053](https://github.com/pure-admin/vue-pure-admin/commit/84060538b48b6426755d0f97ac12ec67bf741249))
- 增加白名单检查函数以优化路由匹配逻辑 ([708cd9b](https://github.com/pure-admin/vue-pure-admin/commit/708cd9b79fecf75a0e7392f68efab0a93b784e31))
- 增加调试日志以跟踪当前路由和查询参数匹配情况 ([54571e8](https://github.com/pure-admin/vue-pure-admin/commit/54571e89dfc2d30e97585477c05ff1deac88d75e))
- 增加更新URL的功能，优化微信回调处理逻辑，确保页面加载后再处理回调 ([deb9e6c](https://github.com/pure-admin/vue-pure-admin/commit/deb9e6c39dbf426ae767f7e59d047dc91abff6f9))
- 增强微信登录错误处理，添加多种特殊状态码的提示和日志记录 ([d181b60](https://github.com/pure-admin/vue-pure-admin/commit/d181b605f488c5ab21b2c774c70ed3ea7446e885))
- 注释掉线上环境删除console的代码 ([9249203](https://github.com/pure-admin/vue-pure-admin/commit/9249203e81944d58783486399746552d6f44fe8d))

## [1.5.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.4.0...v1.5.0) (2025-07-01)

### Features

- 机构基础信息表单多选下拉框及文件上传校验优化 ([2cbbb5a](https://github.com/pure-admin/vue-pure-admin/commit/2cbbb5a8e7f7fa410cbc36e8f8c5733eadb8ac12))

### Bug Fixes

- 财务页面 机构名称无数据用‘--’代替 ([f39a62b](https://github.com/pure-admin/vue-pure-admin/commit/f39a62bf849a4a782f9c07bd2651d09cd94aec8a))
- 机构基础信息表单上传组件及校验逻辑优化 ([a8d1585](https://github.com/pure-admin/vue-pure-admin/commit/a8d158547ce6fcb27f918d7cc7d9ac65bb6b3a08))
- 机构首页滚动后title消失 ([5a0d72a](https://github.com/pure-admin/vue-pure-admin/commit/5a0d72acb95cb7ceab6c879e862d47370bc5f8d4))
- 修复创建时间选择之后再去掉时间，搜索参数依然存在，导致可能搜索数据异常 ([4027749](https://github.com/pure-admin/vue-pure-admin/commit/4027749655f3bdf6b2d55f8b6dbeb182a416f325))
- 银行信息界面无数据显示为”--“ ([acac434](https://github.com/pure-admin/vue-pure-admin/commit/acac434900d625f7f576b529639a35ef7f23d04d))
- 增加分页大小选项，调整默认每页显示条目数为15 ([6285657](https://github.com/pure-admin/vue-pure-admin/commit/6285657aff78ac4c3492ae0e9485270cf92f1f81))

## [1.4.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.3.2...v1.4.0) (2025-07-01)

### Features

- 当期详情和审核详情添加用户协议 ([af8561e](https://github.com/pure-admin/vue-pure-admin/commit/af8561e5fce79b96d5b85ba7a4250efd42e51736))
- 登录二维码 ([adccaf4](https://github.com/pure-admin/vue-pure-admin/commit/adccaf4167524b65f969e4016928252d63a5109f))
- 二维码基础功能添加 ([a1d6fe4](https://github.com/pure-admin/vue-pure-admin/commit/a1d6fe44ccc16406e1be5cfdab2c9f83258e37e1))
- 机构基础信息表单及地图选择功能优化 ([30b751a](https://github.com/pure-admin/vue-pure-admin/commit/30b751a738f5bf8a4931dd3aa535f1980e14ed9b))

### Bug Fixes

- 绑定状态修复 ([e7e5f05](https://github.com/pure-admin/vue-pure-admin/commit/e7e5f0599da4279dee50b84b2a0e4ec88c4765a9))
- 报名人数默认值和购买类型调整 ([b461c26](https://github.com/pure-admin/vue-pure-admin/commit/b461c26449be97aa885dcd6590934c868f0eb3a6))
- 表格操作栏与左边预留间隙 ([3884a78](https://github.com/pure-admin/vue-pure-admin/commit/3884a7892ddd462d4505df116c23d1a5b8525a1f))
- 财务调整余额显示 ([43301b5](https://github.com/pure-admin/vue-pure-admin/commit/43301b5a45b50db38911444f89de07fb6169ec3a))
- 参与人数展示 ([8fa2030](https://github.com/pure-admin/vue-pure-admin/commit/8fa2030cd1292f44181402ca3cfa8d7df107a1d7))
- 当期详情切换tabs文案调整 ([5db2c19](https://github.com/pure-admin/vue-pure-admin/commit/5db2c1918c3e4cdf20bc5f852e36d7082ff59c00))
- 调整财务表格样式 ([52ac053](https://github.com/pure-admin/vue-pure-admin/commit/52ac053fe620cf5bf470675a07f9f21764477449))
- 调整拦截器逻辑 ([ed24fde](https://github.com/pure-admin/vue-pure-admin/commit/ed24fdeb5ae8161dfe9d705218593340eccbda91))
- 多图片时删除bug处理 ([f4d083c](https://github.com/pure-admin/vue-pure-admin/commit/f4d083cc120029571a4dc791da6351698c987f3e))
- 发现管理图片上传bug修改 ([33e8872](https://github.com/pure-admin/vue-pure-admin/commit/33e887282521363e9fcc83b79a9d993f16d71de5))
- 发现管理文案修改 ([e361dd9](https://github.com/pure-admin/vue-pure-admin/commit/e361dd93642e11f4057030c3bf2281d516fcb916))
- 发现管理新增添加图片上传格式 ([b40d107](https://github.com/pure-admin/vue-pure-admin/commit/b40d107b797377863ce27358c4a42a081e1a4fa4))
- 机构管理上传最佳图片规格修改 ([a0aa3f1](https://github.com/pure-admin/vue-pure-admin/commit/a0aa3f1467f3ace54f4e8046d7c952e14a2ce147))
- 机构基础信息表单上传文件说明 ([1237715](https://github.com/pure-admin/vue-pure-admin/commit/123771500dc5733ed1657b4f2e96a0ffc1e55f17))
- 家长/学生管理列表bug ([dc0680b](https://github.com/pure-admin/vue-pure-admin/commit/dc0680b6a8506306671c4448d1bbce58d3fcc9ea))
- 讲师、领队详情回显 ([2ae3b88](https://github.com/pure-admin/vue-pure-admin/commit/2ae3b8855d0e70d7233e281f71c85aca4436a1e9))
- 禁用机构审核实践点审核搜索搜索功能 ([1db81ad](https://github.com/pure-admin/vue-pure-admin/commit/1db81ad19bd13f75616a3c17dbe17911c13d78c9))
- 局端账号输入框长度限制 ([7bb09a9](https://github.com/pure-admin/vue-pure-admin/commit/7bb09a920d885ca4eef6f5509f8554c87d1daa4d))
- 课程管理、课程审核、账号管理padding调整 ([862be58](https://github.com/pure-admin/vue-pure-admin/commit/862be58b13f8327c1c1db4461d1ea97a57fe2b76))
- 课程详情添加课程标签及作业设计无数据调整 ([ed92398](https://github.com/pure-admin/vue-pure-admin/commit/ed9239837c8fad4f79b87bcb656bf1cfd24aa136))
- 课程详情中图片展示问题 ([ab153bc](https://github.com/pure-admin/vue-pure-admin/commit/ab153bcc31e43137bfc64f439d00d4c31a5def50))
- 领队管理，机构管理文件上传调整2.0 ([1fcf195](https://github.com/pure-admin/vue-pure-admin/commit/1fcf1955f3c2f4557e73d113de70fae89fdccf65))
- 领队管理中的编辑信息里的文件上传调整 ([0f117f7](https://github.com/pure-admin/vue-pure-admin/commit/0f117f772b6fd5236a0243b151df530d48f0812d))
- 领队讲师缺少机构选项及搜索输入框去空 ([e4ddc30](https://github.com/pure-admin/vue-pure-admin/commit/e4ddc30397938ddf3aea0a443b27b58be5ffee91))
- 领队讲师账号创建编辑校验及限制 ([cbb5189](https://github.com/pure-admin/vue-pure-admin/commit/cbb5189e65f83ab466f20bb62ac5298d60e11b91))
- 轮播图/热门课程/精品课程的日志修改 ([889dfdf](https://github.com/pure-admin/vue-pure-admin/commit/889dfdff55d638454f31fc77f4645c159a8932c3))
- 平台设置中上移/下移展示调整 ([2800f29](https://github.com/pure-admin/vue-pure-admin/commit/2800f2901c6a6e845db49c9ac6b72cf2ae959bd8))
- 平台信息UI调整 ([87355b6](https://github.com/pure-admin/vue-pure-admin/commit/87355b6ec3fa16d11d6534543153cc09e72703ae))
- 全部评价中的人数限制改为课程标签 ([3db392d](https://github.com/pure-admin/vue-pure-admin/commit/3db392d19e229f9010ebd5e9c181b67ef8967e32))
- 身份证验证调整 ([593d398](https://github.com/pure-admin/vue-pure-admin/commit/593d3985940b2d493d11b313771f56c7b72c28ad))
- 首页平台账号加输入框长度限制及身份证号验证 ([6b96972](https://github.com/pure-admin/vue-pure-admin/commit/6b9697262197324fda0887441c04c5992716746f))
- 添加客服热线校验逻辑 ([6c9fd20](https://github.com/pure-admin/vue-pure-admin/commit/6c9fd20e1855bac9686ed722d83b89818f211ce6))
- 文件不支持预览提示修改 ([9eebe11](https://github.com/pure-admin/vue-pure-admin/commit/9eebe11cfa2d07b82b79556a3c54d5ee74dc2c36))
- 文件上传限制统一调整 ([9846d02](https://github.com/pure-admin/vue-pure-admin/commit/9846d02d461701c67198dc0f273f868b4ea7a185))
- 修复编辑用户名主页不同步异常 ([3a5f739](https://github.com/pure-admin/vue-pure-admin/commit/3a5f7399a722778af05a541fa8324abe02510626))
- 修复登录组件中loading状态异常 ([2cabd3e](https://github.com/pure-admin/vue-pure-admin/commit/2cabd3e48d70bcc9766bef9e473eef46dc955077))
- 修复机构编辑页面账号无法修改异常 ([46fa23b](https://github.com/pure-admin/vue-pure-admin/commit/46fa23bf73e4bcfe12f485d88bcfcf5059a07aff))
- 修复角色管理中名称和描述字段去空格处理, 调整日志板块样式 ([5752ff2](https://github.com/pure-admin/vue-pure-admin/commit/5752ff25199444a1680a3b818184e52f2fff31e1))
- 修改 ([09aac1a](https://github.com/pure-admin/vue-pure-admin/commit/09aac1a1383d3817ec498bc54c76e00dea6fbab3))
- 修改机构操作日志文案 ([476aab4](https://github.com/pure-admin/vue-pure-admin/commit/476aab47c151cae9d16bfa502487859cbb6eed68))
- 修改身份号字段标签为身份证号码 ([dd4f57f](https://github.com/pure-admin/vue-pure-admin/commit/dd4f57f1157c5fe7e943d894d0b30b72e3598983))
- 修改微信登录code码配置方式，修改相关日志文案 ([73b5a51](https://github.com/pure-admin/vue-pure-admin/commit/73b5a514afcb15665a1d94a31d994e4dd6b3ff50))
- 学生/家长管理课程详情数据接口调整 ([276d59d](https://github.com/pure-admin/vue-pure-admin/commit/276d59d0a242a0b1de01997dedf35dca5b80c61f))
- 学生管理首页搜索框去空格及重置功能调整和家长管理首页重置功能调整 ([e54f20e](https://github.com/pure-admin/vue-pure-admin/commit/e54f20e4da09d97cb0365678bae13f7027743d0e))
- 学生情况、课程详情、评价分页器统一 ([3de582a](https://github.com/pure-admin/vue-pure-admin/commit/3de582a8b6a7a1114983271050899438bb7e3dea))
- 用户评价按钮优化 ([93d9d09](https://github.com/pure-admin/vue-pure-admin/commit/93d9d09edbad94a2264277bae65e63c57021605b))
- 优化机构操作日志文案 ([319eb50](https://github.com/pure-admin/vue-pure-admin/commit/319eb507d3c1a1eebfe94d7fd0004b4bb64afe05))
- 优化平台账号详情及编辑身份证显示 ([dc2a31d](https://github.com/pure-admin/vue-pure-admin/commit/dc2a31d9203feca961ab6ec9fb95bdbf3638a7dd))
- 优化日志重置和清除日期逻辑 ([c4ac926](https://github.com/pure-admin/vue-pure-admin/commit/c4ac9268173e2f7700d8c02577ddeafae6779d5e))
- 注释掉可提现金额统计项 ([e0bd139](https://github.com/pure-admin/vue-pure-admin/commit/e0bd1398e679555d53fd9f4df436ad516c0c1913))
- 专家库批量导入bug ([0642f50](https://github.com/pure-admin/vue-pure-admin/commit/0642f50d5f3c59725eaedd9c590e8f167b563b39))
- 作业详情和评价详情无图片数据展示空状态（bug963、964） ([95864a7](https://github.com/pure-admin/vue-pure-admin/commit/95864a7f4ec464e7d211ee9ad5fbade6d56eee9a))

### [1.3.2](https://github.com/pure-admin/vue-pure-admin/compare/v1.3.1...v1.3.2) (2025-06-20)

### [1.3.1](https://github.com/pure-admin/vue-pure-admin/compare/v1.3.0...v1.3.1) (2025-06-17)

### Bug Fixes

- 补充验证码按钮倒计时 ([f880b6d](https://github.com/pure-admin/vue-pure-admin/commit/f880b6d64bb1563396e1f424ddc91b4c7ef26525))
- 调整手机号和验证码输入框宽度 ([1c4833d](https://github.com/pure-admin/vue-pure-admin/commit/1c4833d31f5bd4b8961643bb948673164eed7b53))
- 讲师、领队管理信息编辑修复 ([0b077ab](https://github.com/pure-admin/vue-pure-admin/commit/0b077ab3cdb2c512b2125a32af7761930804eaaa))
- 禁用新建机构多余接口调用 ([dc1152c](https://github.com/pure-admin/vue-pure-admin/commit/dc1152c5efbb894d247485704c9f04c1341af375))
- 课程知识点等调整为课期知识点等 ([b8b5eb5](https://github.com/pure-admin/vue-pure-admin/commit/b8b5eb54daf06584cc79b075123a3d3d5c550926))
- 日志补充 ([9447313](https://github.com/pure-admin/vue-pure-admin/commit/9447313cc6e335462544f5e2b40eacebd8a0def8))
- 日志信息处理 ([abb5a62](https://github.com/pure-admin/vue-pure-admin/commit/abb5a628665d07e1f3d1e18912bcfdbdebce722a))
- 修改平台信息上传提示文案 ([a8c1fd0](https://github.com/pure-admin/vue-pure-admin/commit/a8c1fd00641cd00e7d78db77d209feea9c31f1b5))
- 优化富文本编辑器粘贴重复问题 ([7509946](https://github.com/pure-admin/vue-pure-admin/commit/750994687b625554cbb2d534f43bfda708cc5d0b))
- 余额字段修改 ([dc6cbe6](https://github.com/pure-admin/vue-pure-admin/commit/dc6cbe63a78b748b4efab108c4eec890e52d9f3e))

## [1.3.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.2.1...v1.3.0) (2025-06-16)

### Features

- 使用 el-statistic 组件优化财务数据展示 ([9c06dec](https://github.com/pure-admin/vue-pure-admin/commit/9c06dec75fff3c00709172a49d6d9130bfee37b0))

### Bug Fixes

- 财务管理显示优化 ([644ead3](https://github.com/pure-admin/vue-pure-admin/commit/644ead37037ef2fd0d5547144ccc55af4716039a))
- 更新订单状态处理逻辑，支持申请退款状态 ([ac67a71](https://github.com/pure-admin/vue-pure-admin/commit/ac67a71f81d1baf58161e2fe8f9789054db449c4))
- 更新手机号验证接口路径，添加验证码功能及相关逻辑 ([c79913c](https://github.com/pure-admin/vue-pure-admin/commit/c79913ccbc27f431da3d812447c6cc831ea5bdd5))
- 平台设置 tab页 多接口调用修复 ([cd0f48d](https://github.com/pure-admin/vue-pure-admin/commit/cd0f48d1b52b6178c8708e0a3cdc3ba063dad493))
- 页面的显示方式 ([f1cf554](https://github.com/pure-admin/vue-pure-admin/commit/f1cf554d124a81587f71a69058a663097fd6fe93))
- 优化财务数据展示 ([35a43f6](https://github.com/pure-admin/vue-pure-admin/commit/35a43f68eaa2e6f3cedca134860d35314183efb0))

### [1.2.1](https://github.com/pure-admin/vue-pure-admin/compare/v1.2.0...v1.2.1) (2025-06-13)

### Bug Fixes

- 环境配置 ([bb23997](https://github.com/pure-admin/vue-pure-admin/commit/bb239973e4c78bc331584ee69f2ef90bd0557794))

## [1.2.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.1.1...v1.2.0) (2025-06-13)

### Features

- 调整各管理模块的分页大小至15，并添加动态调整功能 ([06c83d9](https://github.com/pure-admin/vue-pure-admin/commit/06c83d9e1d082b00dfbc923b01c69ac46cff2cc4))
- 丰富富文本编辑器功能，支持自定义粘贴处理器及默认文本颜色设置 ([4377760](https://github.com/pure-admin/vue-pure-admin/commit/43777602934dc011d5ded7c440b824d5a0ff81c6))
- 课程分类新增图片上传 ([dc88666](https://github.com/pure-admin/vue-pure-admin/commit/dc886669e81b13d963b0c0c1518c65ac74e2e92a))
- 添加文件Excel、Word、PDF和PPTX格式预览组件 ([6ac9e4b](https://github.com/pure-admin/vue-pure-admin/commit/6ac9e4b3a9c27945fee143bebb0b105e6fc6b79a))
- 新增平台信息获取功能，优化图标和标题设置 ([3eefe79](https://github.com/pure-admin/vue-pure-admin/commit/3eefe795eb9c2cb9f149bda95656a56d79ba7498))
- 新增文件上传大小限制功能 ([8732e68](https://github.com/pure-admin/vue-pure-admin/commit/8732e683bcd65f4182d722740034874ab64f51eb))
- 新增银行账户信息页面 ([245dcb7](https://github.com/pure-admin/vue-pure-admin/commit/245dcb741c33e4f73d0dea57f936b355833df4bd))
- 新增Office文档预览支持 ([fc72f15](https://github.com/pure-admin/vue-pure-admin/commit/fc72f15091435ad592ad60590ef1d66518336ae1))
- 修复课程分类重复请求 ([0da8d0e](https://github.com/pure-admin/vue-pure-admin/commit/0da8d0e56597a68c7e183e8922edda35acdab581))
- 验证码登录 ([510672d](https://github.com/pure-admin/vue-pure-admin/commit/510672dc3fb90525db6f41a4e937e66c58c16bc0))
- 优化账户信息提交逻辑，添加空值移除和条件处理 ([0f4df9d](https://github.com/pure-admin/vue-pure-admin/commit/0f4df9d5c46727b7d6a2d69296e03bab46f6ffde))

### Bug Fixes

- 报错处理 ([9901784](https://github.com/pure-admin/vue-pure-admin/commit/99017845853316a3a54d0f35be6fbe4723026970))
- 电话校验兼容400客服热线 ([c7183c5](https://github.com/pure-admin/vue-pure-admin/commit/c7183c5eb558cdaca44547e158ff5c611669df39))
- 调整退单操作日志的显示内容 ([5419e1a](https://github.com/pure-admin/vue-pure-admin/commit/5419e1aae8da393e709b7827eba0cabb803475a9))
- 更新密码验证规则允许使用点号字符 ([a022cc2](https://github.com/pure-admin/vue-pure-admin/commit/a022cc2932ba54e2963df5bf9e12ff77c9bf43e6))
- 课程审核封面 ([aa3c3e4](https://github.com/pure-admin/vue-pure-admin/commit/aa3c3e4a9797a64e1b77580a18724cb95d19bf8c))
- 日志管理全选 ([6ac3d92](https://github.com/pure-admin/vue-pure-admin/commit/6ac3d92dd447236837a8f63142f035db54618c19))
- 为退款相关表单项添加统一样式类并设置宽度" ([3b6de36](https://github.com/pure-admin/vue-pure-admin/commit/3b6de3673e4b8be2affd5e4489cf20b4c08c7148))
- 文件上传类型和大小校验 ([85bc10e](https://github.com/pure-admin/vue-pure-admin/commit/85bc10e10e32bdbe2d6207785ad669a31a42dc2c))
- 修正课堂跟踪标签名称 ([4f3b06a](https://github.com/pure-admin/vue-pure-admin/commit/4f3b06a288fd406644f8465a6424d0043dc0cc48))
- 修正上课跟踪标标签称及订单详情展示 ([a116c59](https://github.com/pure-admin/vue-pure-admin/commit/a116c5985b36d5f6a56f04c3f8e3f6e99bd41f1f))
- 修正上课跟踪标签名称 ([5320a46](https://github.com/pure-admin/vue-pure-admin/commit/5320a46dfa58ab5f0ef0130194fe66067ab56584))
- 修正文件类型映射中的Word和PPTX类型 ([e6ed211](https://github.com/pure-admin/vue-pure-admin/commit/e6ed211c06cb55ce4d90c56f2e67ef7c9201910c))
- 银行信息调整样式 ([49f1925](https://github.com/pure-admin/vue-pure-admin/commit/49f1925de9397460038bdab5a810ac23bd721041))
- 优化获取验证码逻辑 ([4ed2671](https://github.com/pure-admin/vue-pure-admin/commit/4ed2671f75081b5d4f3ff769bdb02a677770c949))
- 优化课程学生情况表格规格显示格式 ([78a0ac1](https://github.com/pure-admin/vue-pure-admin/commit/78a0ac105fd5326ab021ee0fefe1331b7fa0c0e0))
- 优化平台账户信息展示，调整分页 ([c26654e](https://github.com/pure-admin/vue-pure-admin/commit/c26654ecb2a8d2dc5bd038a937afa24304c707b6))
- 注释 ([2c7acb5](https://github.com/pure-admin/vue-pure-admin/commit/2c7acb5a0f1825b6e819740425161cb45c0a9c1d))
- 注释掉token刷新逻辑 ([7888378](https://github.com/pure-admin/vue-pure-admin/commit/78883785fba0fe52dff8d4025dc32eb60840fd62))
- api地址修改 ([1d58eba](https://github.com/pure-admin/vue-pure-admin/commit/1d58eba5a2673c053bc8a06b80925a5420325f56))

### [1.1.1](https://github.com/pure-admin/vue-pure-admin/compare/v1.1.0...v1.1.1) (2025-05-28)

### Bug Fixes

- 账号创建失败 ([3cd535a](https://github.com/pure-admin/vue-pure-admin/commit/3cd535a0b76a36fe05157f20d8791ced0d87188a))

## [1.1.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.0.0...v1.1.0) (2025-05-27)

### Features

- 发现管理 ([2ce80cd](https://github.com/pure-admin/vue-pure-admin/commit/2ce80cd925341804a57d3e202fbba351bac556c3))
- 课程当期详情新增表单收缩功能 ([41c376c](https://github.com/pure-admin/vue-pure-admin/commit/41c376c9d4c2c9ce8500a15f2f1ee4432ad98f44))
- 课程管理增加展示条目 ([f06168c](https://github.com/pure-admin/vue-pure-admin/commit/f06168c2f9682fb887806e6c1c3284d7a531cb5e))
- 全部评价功能 ([dc95de9](https://github.com/pure-admin/vue-pure-admin/commit/dc95de91272b6c94acd19c3169628118cc1f6125))
- 添加图片代理功能及缩略处理 ([6eb1081](https://github.com/pure-admin/vue-pure-admin/commit/6eb1081884fc41c50f3c5ff142697026bec70c4a))
- 账号创建编辑添加验证功能 ([f5c8d06](https://github.com/pure-admin/vue-pure-admin/commit/f5c8d065879dfcd3aca73d61a5f67a6156170e9a))

### Bug Fixes

- 当期详情详情内容及关联订单课期详情内容调整 ([671f9c9](https://github.com/pure-admin/vue-pure-admin/commit/671f9c9cd8743fd270220f779ffbd316d62a867e))
- 点击遮罩层关闭图片预览 ([8911df4](https://github.com/pure-admin/vue-pure-admin/commit/8911df402ae36218271adb9dcbdc9526eaf16859))
- 订单详情ui异常 ([12ac144](https://github.com/pure-admin/vue-pure-admin/commit/12ac144ef9e4d1c8f2a1f4b4f1213e812a58aedc))
- 多个页面样式调整，修改容器类名及内边距 ([4e8163e](https://github.com/pure-admin/vue-pure-admin/commit/4e8163efc6334decb090c1bad03044db9da8eec7))
- 多个页面样式调整，修改容器类名及内边距 ([6bec0a4](https://github.com/pure-admin/vue-pure-admin/commit/6bec0a4e991d9356fac1fa4b92c3aee3dc3475e9))
- 二次确认弹框统一 ([50cd70f](https://github.com/pure-admin/vue-pure-admin/commit/50cd70fb9488ae4cc63fc6c4526c140896cf8b43))
- 发现-新增按钮位置调整 ([f3be57b](https://github.com/pure-admin/vue-pure-admin/commit/f3be57b489fec2554477c58b74d51009bddb9b08))
- 发现-修复内容展示 ([5a2bfb1](https://github.com/pure-admin/vue-pure-admin/commit/5a2bfb181acbb929010d673cc899c5404c3821c6))
- 发现管理列表UI调整 ([3302ff3](https://github.com/pure-admin/vue-pure-admin/commit/3302ff39a169c3db8784801da20d3e823b1c44d2))
- 发现管理列表UI调整2.0 ([29f18b1](https://github.com/pure-admin/vue-pure-admin/commit/29f18b1d4890628f414571506f70aa1d1a9c0e33))
- 富文本拼接 ([7fd5c73](https://github.com/pure-admin/vue-pure-admin/commit/7fd5c73f5bd3d12754418158d13a40a4de2e2831))
- 关联订单详情页调整 ([cb49c06](https://github.com/pure-admin/vue-pure-admin/commit/cb49c063f0c72137ad22e5d529b22191051858a9))
- 基地机构审核ui调整 ([71e2028](https://github.com/pure-admin/vue-pure-admin/commit/71e2028bca1228c209d77fb9cd314c5f82f4a9fd))
- 家长/学生-相关订单 ([927b4c4](https://github.com/pure-admin/vue-pure-admin/commit/927b4c47e421edc1cd04d8c7eb13a749772b7414))
- 家长/学生-作业情况+评价 ([ac5f829](https://github.com/pure-admin/vue-pure-admin/commit/ac5f829786d1895984da8e14f67b80349d7d2eef))
- 局端和平台账号输入优化 ([70f141a](https://github.com/pure-admin/vue-pure-admin/commit/70f141abf5678cd323a707151053addc80e06a13))
- 客服热线校验 ([0ccf433](https://github.com/pure-admin/vue-pure-admin/commit/0ccf433059d2023ab58b105b0274a5af43672a01))
- 课程当期详情图片表单排版调整 ([be79901](https://github.com/pure-admin/vue-pure-admin/commit/be799018346529d2dd69b9102ebd1fd7286e17fb))
- 课程管理样式调整 ([4053d79](https://github.com/pure-admin/vue-pure-admin/commit/4053d7947d23f4c40a8ce5a43f2d2b5f0989701d))
- 课程审核详情不展示成团人数 ([c879f4a](https://github.com/pure-admin/vue-pure-admin/commit/c879f4a9b91031e6267ad5eb5998375e34f26f4d))
- 课程详情页课程分类与封面图展示优化 ([7c36fdc](https://github.com/pure-admin/vue-pure-admin/commit/7c36fdc5a0621bbcc737a62855b736b5aaf4d861))
- 课期详情ui异常 ([872a294](https://github.com/pure-admin/vue-pure-admin/commit/872a294f291409b1045a7f2957cd2f4a18bf684f))
- 领队详情创建时间文案调整 ([40232d9](https://github.com/pure-admin/vue-pure-admin/commit/40232d9c3ce9b1f8334d5940f378c354424d29d0))
- 领队详情排版 ([899ef03](https://github.com/pure-admin/vue-pure-admin/commit/899ef03727ebdeb5a1e8032bcf14325e669196aa))
- 评价数量不对 ([2e34817](https://github.com/pure-admin/vue-pure-admin/commit/2e348176427cf8fdb50c6bb0f1f2ee80f2559ba8))
- 屏蔽富文本上传图片/视频 ([475bfa9](https://github.com/pure-admin/vue-pure-admin/commit/475bfa95000eb115445e59513daa2c79ddd2a2b9))
- 屏蔽富文本上传图片+标题字数 ([2683335](https://github.com/pure-admin/vue-pure-admin/commit/26833357d5019970f0d4a9cc6ad30a46105bf5d8))
- 日志表单排版 ([ac4036c](https://github.com/pure-admin/vue-pure-admin/commit/ac4036c356db8836f1f4df03b3b9ad571a767713))
- 日志管理列表UI调整 ([3ccdd26](https://github.com/pure-admin/vue-pure-admin/commit/3ccdd26b78b43109f812d704632b526233a66110))
- 图片代理正式服地址 ([4629f8a](https://github.com/pure-admin/vue-pure-admin/commit/4629f8a72fadc17dc1f03a002ec00e6d0c4cd27b))
- 图片上传数量提示 ([95400af](https://github.com/pure-admin/vue-pure-admin/commit/95400af22fad11ada31f432633e9e4f5ee0bf5fc))
- 图片缩放单位 ([3029782](https://github.com/pure-admin/vue-pure-admin/commit/30297828d51f226861e3f4fa94343bc7a34bb377))
- 图片缩略处理 ([1a5e983](https://github.com/pure-admin/vue-pure-admin/commit/1a5e983c95288389e4fdde8b6577a4e641ea19ce))
- 退单详情状态更新 ([29f3086](https://github.com/pure-admin/vue-pure-admin/commit/29f308636a16a11cf75a62c92c28b99fe0d3befd))
- 下载师资库导入 ([89a6150](https://github.com/pure-admin/vue-pure-admin/commit/89a6150fdbe25eca5f787fdc4022cb023a054197))
- 样式调整 ([6d90e52](https://github.com/pure-admin/vue-pure-admin/commit/6d90e526b88c6bc42e6ad391ab4437d538d07f7d))
- 样式调整 ([e29492c](https://github.com/pure-admin/vue-pure-admin/commit/e29492c6fead508c2b4e07c880e8956ff0e924a4))
- 样式调整（bug） ([2228191](https://github.com/pure-admin/vue-pure-admin/commit/2228191bd885f02753cb683cfc8f75b78c5a35ef))
- 样式回退 ([439c6ee](https://github.com/pure-admin/vue-pure-admin/commit/439c6eebb6adbac6c47304283287ff4638a03471))
- 隐藏页面下面的角码 ([23dcfa7](https://github.com/pure-admin/vue-pure-admin/commit/23dcfa74d3583522f5b8dc7a496d427c037c2b1e))
- 最低留白高度调整(发现,角色,权限) ([e4db8bd](https://github.com/pure-admin/vue-pure-admin/commit/e4db8bda6b80d8cd69daff1ea81b645f976eff00))
- 最低留白高度调整(基地管理,) ([316ea3c](https://github.com/pure-admin/vue-pure-admin/commit/316ea3c0e8f76dcad2ef5de9d00c787146cff1b8))
- 最低留白高度调整(讲师/领队/订单管理,) ([23631df](https://github.com/pure-admin/vue-pure-admin/commit/23631df721f7ff44c61b3c3de1bf5110a39527fc))
- 最低留白高度调整(平台/局端账号,家长学生管理,) ([3043a24](https://github.com/pure-admin/vue-pure-admin/commit/3043a240870ef70481ac2f228e0422acefbc148c))

## [1.0.0](https://github.com/pure-admin/vue-pure-admin/compare/v0.0.6...v1.0.0) (2025-05-15)

### Features

- 领队管理新增冻结相关展示 ([48b77a1](https://github.com/pure-admin/vue-pure-admin/commit/48b77a147c30ee24867733d8d44107f3777f345f))
- 领队新增冻结相关展示 ([17b8579](https://github.com/pure-admin/vue-pure-admin/commit/17b8579ca67533dcba02d9599fe90e4770813e42))
- 添加课程管理、平台账号、局端账号是否冻结筛选项 ([9aff129](https://github.com/pure-admin/vue-pure-admin/commit/9aff129d21be4648c5ae84c4755a028e8e8d7ff7))

### Bug Fixes

- 冻结解冻提示+用户评价文字提示 ([c2a194f](https://github.com/pure-admin/vue-pure-admin/commit/c2a194f2ddd25a80df5bdfe8efce74f471811cd1))
- 基地联系电话默认展示 ([990bdb8](https://github.com/pure-admin/vue-pure-admin/commit/990bdb86e63e25bb34c14ed4789824f186d5c8fc))
- 家长管理列表显示冻结状态,详情增加显示冻结状态和功能按钮 ([bd5431a](https://github.com/pure-admin/vue-pure-admin/commit/bd5431a9d6e0173f139eed3fe150f417be097ff5))
- 家长管理账号状态搜索 ([40f2956](https://github.com/pure-admin/vue-pure-admin/commit/40f2956794b5033080bcaa176aec3f45ac9643e8))
- 课程详情表格UI调整 ([3d34583](https://github.com/pure-admin/vue-pure-admin/commit/3d34583c0d83672e81fa0f12f4916208d2989f73))
- 平台账号详情页解冻冻结 ([964cbda](https://github.com/pure-admin/vue-pure-admin/commit/964cbdadf7d3253eeb24f60804b76df02ed9c4d4))
- 人数限制调整 ([1fed448](https://github.com/pure-admin/vue-pure-admin/commit/1fed44899b917f33c46398ff3575bf9ca1609328))
- 新建账号文案调整 ([59b2480](https://github.com/pure-admin/vue-pure-admin/commit/59b2480435bcc3c214b3066cb3787b8dcde1e5f2))
- 学生情况添加报名人数及人数上限 ([cd7149c](https://github.com/pure-admin/vue-pure-admin/commit/cd7149c0e13c2dc5c8928e883e43bfba523880ce))
- 用户密码新增输入上限 ([9f0e4ee](https://github.com/pure-admin/vue-pure-admin/commit/9f0e4ee6244bfd2c4781bfe4efc91e4a14ea5c15))
- 用户评价评价内容及机构回复展示调整、边距调整 ([27419ac](https://github.com/pure-admin/vue-pure-admin/commit/27419acc4804177b997925a975664f01106692ca))

### [0.0.6](https://github.com/pure-admin/vue-pure-admin/compare/v0.0.5...v0.0.6) (2025-05-09)

### Features

- 安装vue-inspector插件，看到这条commit请执行npm install ([cd1ac0b](https://github.com/pure-admin/vue-pure-admin/commit/cd1ac0b837c01491f698e3238396f4942184d8a0))
- 登录新增日志 ([3bbe21b](https://github.com/pure-admin/vue-pure-admin/commit/3bbe21bbe9db089835b8c5e6fcd00ee38b26ae5e))
- 机构管理文件上传限制 ([7e22857](https://github.com/pure-admin/vue-pure-admin/commit/7e22857acb9c37dbae29f687a3b9c14654f87ddc))
- 角色管理按钮级权限 ([232631e](https://github.com/pure-admin/vue-pure-admin/commit/232631e8f6d1e32f52eb82dbc4875a2eaa802938))
- 局端权限 ([95fd2a0](https://github.com/pure-admin/vue-pure-admin/commit/95fd2a0f6efb3353ef0920ab9dfab451bfb625d7))
- 新增专家库框架 ([61116ed](https://github.com/pure-admin/vue-pure-admin/commit/61116ed1cf041a75898a8be7061b0dcfb8af2d8a))
- 专家库 ([31a4dd1](https://github.com/pure-admin/vue-pure-admin/commit/31a4dd1519a7229c6ad3ee6aea4820858e9b5bb3))

### Bug Fixes

- 领队详情上课时间(bug) ([c8952c7](https://github.com/pure-admin/vue-pure-admin/commit/c8952c77911956b5fcfa364a9489c60fe3912ea8))
- 确定提示调整2.0 ([376d71e](https://github.com/pure-admin/vue-pure-admin/commit/376d71eab004f391d73fd9e440a9cfd9c408f0aa))
- 退单按钮条件修改 ([0644163](https://github.com/pure-admin/vue-pure-admin/commit/064416338eb2528ae4ea9969cac320638bf47e0b))
- : 领队详情添加脱敏(bug) ([1e1d455](https://github.com/pure-admin/vue-pure-admin/commit/1e1d4559df7d1d431a2d3c6571c2327a216ebb66))
- 按钮防抖 ([3b05264](https://github.com/pure-admin/vue-pure-admin/commit/3b05264b4d8d644f042e2e1c55184a2deb2bb0f4))
- 表单排版 ([ef266ec](https://github.com/pure-admin/vue-pure-admin/commit/ef266ec7493dc4a5df5d115f82cd4e78805d7e23))
- 表格内容过多超出显示优化 ([23f2cd4](https://github.com/pure-admin/vue-pure-admin/commit/23f2cd495e794e93ba9a73466c9ad04189a13aa1))
- 代码整理 ([f15a398](https://github.com/pure-admin/vue-pure-admin/commit/f15a39851f6d9bac4631e01a4aec84366955e2a5))
- 代码整理2.0 ([0cdda90](https://github.com/pure-admin/vue-pure-admin/commit/0cdda906a72976b097d472f26709dc453d39cd81))
- 代码整理3.0 ([e764c02](https://github.com/pure-admin/vue-pure-admin/commit/e764c026a452f35e196eb11aff61fa81768f6f8d))
- 登录密码验证 ([e917b85](https://github.com/pure-admin/vue-pure-admin/commit/e917b854faf3eae451023a205c38ba832c5b50c8))
- 登录异常文案，局端按钮级权限释放 ([ac31e96](https://github.com/pure-admin/vue-pure-admin/commit/ac31e96684512b56e2a008c610eb6c57cd4f508e))
- 调整课期内容展示 ([9d04b58](https://github.com/pure-admin/vue-pure-admin/commit/9d04b58d5215ea28c59ebf5e4949d2bfccdedfb5))
- 订单管理调整 ([af0e085](https://github.com/pure-admin/vue-pure-admin/commit/af0e085ec6c0ea4f4c3a20507dbb7874aed2c34f))
- 订单管理滚轮调整 ([01448cf](https://github.com/pure-admin/vue-pure-admin/commit/01448cf93aa4c284dd7695f33ac2fc19698b16b5))
- 订单管理修改筛选项为订单状态和退款状态 ([7980308](https://github.com/pure-admin/vue-pure-admin/commit/79803087bb029abd710bbcdfc6c9677cb867a48e))
- 订单详情调整 ([fc26025](https://github.com/pure-admin/vue-pure-admin/commit/fc260256d5140a0a12380d48234849e5813fd060))
- 订单详情退单功能调整 ([9e38047](https://github.com/pure-admin/vue-pure-admin/commit/9e38047581b14b9c9bf9213e0ef8d1a6cb297beb))
- 订单详情修改 ([eb87499](https://github.com/pure-admin/vue-pure-admin/commit/eb87499d6a00e8c794c320cbbf6a410f3bbb4021))
- 订单详情状态调整 ([baeb85c](https://github.com/pure-admin/vue-pure-admin/commit/baeb85cab1945d3852000912376bb1cd60cab6b4))
- 二次确认弹框样式统一 ([b339bd8](https://github.com/pure-admin/vue-pure-admin/commit/b339bd8897d572c830fedfee31e3fe43633629e7))
- 分类 ([d315582](https://github.com/pure-admin/vue-pure-admin/commit/d315582363239624c48fc36e30f0f0c084f9b0c9))
- 分类管理-弹框确认按钮防抖 ([73c88ef](https://github.com/pure-admin/vue-pure-admin/commit/73c88efdda5a2d57839d56cdd67c2543eea3b5ab))
- 分类管理页面缓存 ([490ba5c](https://github.com/pure-admin/vue-pure-admin/commit/490ba5c1e378196d23b08cef5de4db26e4b2e7ed))
- 分页(bug) ([11371af](https://github.com/pure-admin/vue-pure-admin/commit/11371afe5dcaeb7c20f5ce0cc91b5ba06c650098))
- 各模块创建时间筛选调整及课程管理首页埋点 ([947c273](https://github.com/pure-admin/vue-pure-admin/commit/947c273c68a66ae6fd81e78f6cbf01cfa91b3437))
- 购买类型及课期审核状态和筛选调整 ([ca999ca](https://github.com/pure-admin/vue-pure-admin/commit/ca999ca70e8b1c2cf4b0c3fd42224121967493d1))
- 关闭团单及下架调整 ([e8162ed](https://github.com/pure-admin/vue-pure-admin/commit/e8162edaa35389c72763ad2bd7b116e56267455b))
- 关闭团购接口 ([0d60b2d](https://github.com/pure-admin/vue-pure-admin/commit/0d60b2d8e9497db25c67e1c5005821d311812c2f))
- 关联订单课期信息调整 ([a2a40d9](https://github.com/pure-admin/vue-pure-admin/commit/a2a40d9f11eb3d0c76f92df88acb045c4acb4547))
- 机构财务支付金额(bug) ([c1485d0](https://github.com/pure-admin/vue-pure-admin/commit/c1485d0b0733fc9a83b519bc34c616678bd378fa))
- 机构管理，基地管理缓存调整 ([0486072](https://github.com/pure-admin/vue-pure-admin/commit/0486072dfbc8e86ed33494af7b668db03915b39d))
- 机构管理调整 ([378becc](https://github.com/pure-admin/vue-pure-admin/commit/378becc9ad5275834e0ef8f1ec7e7a0b67663daf))
- 机构管理文件上传限制修改 ([d3ba1a4](https://github.com/pure-admin/vue-pure-admin/commit/d3ba1a4ac86abfaf3322d54c4d51f4b9a840d092))
- 基地详情-基地联系电话(bug) ([9acb555](https://github.com/pure-admin/vue-pure-admin/commit/9acb555d2927764c74a701f6d8729acc286479ce))
- 基地详情-基地联系电话(bug)2.0 ([189ad96](https://github.com/pure-admin/vue-pure-admin/commit/189ad9682a785a528845c8e7a7a92f4db5da832a))
- 家长-订单列表添加退款状态 ([c114f94](https://github.com/pure-admin/vue-pure-admin/commit/c114f949e576b183b21d77d34ad047ca485cacc0))
- 家长订单管理调整 ([28b5d31](https://github.com/pure-admin/vue-pure-admin/commit/28b5d31b5ef7c36c8a61e67e171d83b8d34328e8))
- 价格展示加上单位 ([e245ca0](https://github.com/pure-admin/vue-pure-admin/commit/e245ca0d4dc7aab266e09756318f6916f36c304e))
- 兼容登录异常新状态码 ([2906f9c](https://github.com/pure-admin/vue-pure-admin/commit/2906f9c9fab5ceca378d5e051dba56b502d927ba))
- 讲师\领队\角色\权限样式统一 ([e81288b](https://github.com/pure-admin/vue-pure-admin/commit/e81288b9c6b7efaea34a1ed29e8168e74a366ce9))
- 讲师页面缓存+讲师面包屑 ([a7edbd7](https://github.com/pure-admin/vue-pure-admin/commit/a7edbd790f97dbc4a1624ad4b182b4c7c84f03b5))
- 进入订单详情报错 ([b5a9916](https://github.com/pure-admin/vue-pure-admin/commit/b5a9916664b82aafb495fe5f561d29b2a2e1d298))
- 局端登录 ([3809db5](https://github.com/pure-admin/vue-pure-admin/commit/3809db50b24d416454acf3c3b8a7a79600c3f272))
- 局端和平台账号埋点 ([260791b](https://github.com/pure-admin/vue-pure-admin/commit/260791b2604ee1274d464bd1c4de3bf331139226))
- 课程管理课程审核keep-alive不生效问题 ([0e1a869](https://github.com/pure-admin/vue-pure-admin/commit/0e1a869bf1a7fcc87e7dcff8182145669b126a41))
- 课程管理埋点 ([a6e0f36](https://github.com/pure-admin/vue-pure-admin/commit/a6e0f364aa5abce219f6d510d25f82a8c42b327f))
- 课程管理首页课程冻结状态展示 ([db185d9](https://github.com/pure-admin/vue-pure-admin/commit/db185d9f7a5f3c5d7f242156f278267cd5e37437))
- 课程管理学生情况家长电话调整 ([011e2a0](https://github.com/pure-admin/vue-pure-admin/commit/011e2a0a6cd016a019d2fa5e6b331d3e4f5e13b2))
- 课程管理用户评价分数搜索优化 ([a8b8351](https://github.com/pure-admin/vue-pure-admin/commit/a8b835180a7db30e032cd78c9146f539a47ecbeb))
- 课程描述表格不显示基地 ([27aee16](https://github.com/pure-admin/vue-pure-admin/commit/27aee160d86529d9a46ec36e749247f4ba828c1e))
- 课程审核埋点 ([e8e4796](https://github.com/pure-admin/vue-pure-admin/commit/e8e4796fb050256d5a3bc9f23f32e310a3d12d9d))
- 课程审核审核接口调整 ([02b83c2](https://github.com/pure-admin/vue-pure-admin/commit/02b83c216b6e3a29297ee9fb597d86f685bc2e18))
- 课程审核时间筛选调整 ([7c3ac71](https://github.com/pure-admin/vue-pure-admin/commit/7c3ac71a9d6237515abf8bbda2a63861e22390aa))
- 课程审核系统自动驳回调整 ([1c68fd6](https://github.com/pure-admin/vue-pure-admin/commit/1c68fd661e761131619aa958d8e76d7261948b1a))
- 课程详细资料及学生情况样式异常 ([bca0c58](https://github.com/pure-admin/vue-pure-admin/commit/bca0c58e2a12af0f535b5bde10c44269f99f4054))
- 课期待审核页面审核后刷新可二次审核问题及课程冻结文案及课期下架显示原因 ([e9583e6](https://github.com/pure-admin/vue-pure-admin/commit/e9583e6458ace5f8e81e7a67904dfd7553ac3b7e))
- 领队、教师样式优化 ([237fb37](https://github.com/pure-admin/vue-pure-admin/commit/237fb3794c17d7eb423bf0fc2f7611b235b2b6bd))
- 领队\讲师管理样式+平台轮播图、热门课程、精品课程(bug修改) ([9ed3f9f](https://github.com/pure-admin/vue-pure-admin/commit/9ed3f9faf0fa2c10addafa4ca3a7b2f27dfdef10))
- 领队\讲师页面缓存+ 讲师路由面包屑 ([63d31e0](https://github.com/pure-admin/vue-pure-admin/commit/63d31e04ba3f150773463ce68cfb95087ab79576))
- 领队详情分页(bug) ([e24b741](https://github.com/pure-admin/vue-pure-admin/commit/e24b74127c4c8f6a624bb66240dd7e6771093333))
- 领队详情UI调整 ([b70c235](https://github.com/pure-admin/vue-pure-admin/commit/b70c235074725c0015276166d693ac23d6d98e77))
- 路由配置调整 ([571cec0](https://github.com/pure-admin/vue-pure-admin/commit/571cec01fc75bd54fb412b877bb713e26fee38b9))
- 路由跳转新增缓存 ([5f227f3](https://github.com/pure-admin/vue-pure-admin/commit/5f227f319d71ee218d6a56ab2c446d8d691508f4))
- 路由页面缓存(家长，学生，分类) ([6fed188](https://github.com/pure-admin/vue-pure-admin/commit/6fed18863639279fa3391ef7e463edc47d3835ff))
- 路由页面缓存(家长，学生，分类)2.0 ([9cdec47](https://github.com/pure-admin/vue-pure-admin/commit/9cdec47d64341b80234709551a85b95f7fe59802))
- 轮播图优化 ([1ef4ed7](https://github.com/pure-admin/vue-pure-admin/commit/1ef4ed7e5162294a25e1232d1cada5a3d8a94812))
- 埋点 ([eaf1b9f](https://github.com/pure-admin/vue-pure-admin/commit/eaf1b9f90df8c794b518f47b0d11e1a0e83d4c50))
- 平台设置-图片上传 ([c96d777](https://github.com/pure-admin/vue-pure-admin/commit/c96d77770dc6c25807017b3efee53d2c94e2a7d0))
- 平台设置传参(bug) ([36a5b04](https://github.com/pure-admin/vue-pure-admin/commit/36a5b04215bd6fd92c4169dc6b6afaef246affb1))
- 平台文件上传限制修改 ([f4f79f1](https://github.com/pure-admin/vue-pure-admin/commit/f4f79f1a03efb168281917f82767221b83f1a8a8))
- 平台信息改为非必填 ([f352105](https://github.com/pure-admin/vue-pure-admin/commit/f352105ef423c37301dfd764508d07b69d298c5e))
- 平台账号及局端账号展示账号状态及loading状态添加 ([60cc505](https://github.com/pure-admin/vue-pure-admin/commit/60cc5050db4c914937ce6540156377e6123fe0db))
- 平台账号局端账号缓存keep-alive未生效 ([ac97611](https://github.com/pure-admin/vue-pure-admin/commit/ac9761156e25bcdfe122325819f8ac609cc20e32))
- 全部评价鼠标移入手型 ([8c2e2af](https://github.com/pure-admin/vue-pure-admin/commit/8c2e2af99dbc3f0d37855bd536521121a4aa2c9d))
- 确定提示调整+分类管理删除优化 ([f6106b5](https://github.com/pure-admin/vue-pure-admin/commit/f6106b5a526165a294828c15dca244da435f1f19))
- 确认按钮防抖 ([e4d7565](https://github.com/pure-admin/vue-pure-admin/commit/e4d756537073592495d92904eaa6881fca453163))
- 确认文案调整为确定 ([2599a43](https://github.com/pure-admin/vue-pure-admin/commit/2599a4332b7ac4c1ac2fdd593bdbb3867db34c45))
- 日期搜索修改 ([b400706](https://github.com/pure-admin/vue-pure-admin/commit/b40070694b140c69d73abae63f55b23ca0e06ed7))
- 日志、机构、财务、订单管理缩放表格动态高度 ([3b35bbc](https://github.com/pure-admin/vue-pure-admin/commit/3b35bbc0789ceb3204aa17e43f657a7f8c110d36))
- 日志埋点 ([03d74d7](https://github.com/pure-admin/vue-pure-admin/commit/03d74d744bad92e6098a385f57e10b08224a3899))
- 日志埋点 ([17123fa](https://github.com/pure-admin/vue-pure-admin/commit/17123fae8655b2fd265c9ccfe8e78466e08ba0e5))
- 日志文案调整 ([6be45ac](https://github.com/pure-admin/vue-pure-admin/commit/6be45accf6d12c60ef9f9c327c7aac44ffc45af9))
- 数据过长优化显示 ([32a21a1](https://github.com/pure-admin/vue-pure-admin/commit/32a21a1201bd10db03062fc551fc2aaa6c513295))
- 搜索时间调整 ([e28b0b8](https://github.com/pure-admin/vue-pure-admin/commit/e28b0b85f4b59faa19f96c3ff0c41b4bbc0bcce4))
- 替换默认头像 ([2627272](https://github.com/pure-admin/vue-pure-admin/commit/262727278442138628c999fb4a5047f79736a6e4))
- 退单调整 ([f5e7a94](https://github.com/pure-admin/vue-pure-admin/commit/f5e7a9485758d9b4a99774bd58b30f50aac39e01))
- 文件上传 ([51b3495](https://github.com/pure-admin/vue-pure-admin/commit/51b34952050187c3c41dd3238f9831dd66135dba))
- 无数据课程管理报错 ([54ace24](https://github.com/pure-admin/vue-pure-admin/commit/54ace2464f5074f9b41279f3fd562189a1716c1a))
- 新增密码锁定提示 ([1aa4ee4](https://github.com/pure-admin/vue-pure-admin/commit/1aa4ee46b0ab761aeaaee6f8be2d4826dfdb6e5d))
- 修复富文本图片展示拼接空格 ([e3de6aa](https://github.com/pure-admin/vue-pure-admin/commit/e3de6aacf4e2101ab2155ce18b12b0c4265f6c4b))
- 修复了日志只在成功后请求 ([1703829](https://github.com/pure-admin/vue-pure-admin/commit/17038294b18c50c10d56a31afba7187487709c33))
- 修复领队、讲师管理日志 ([c83cb7d](https://github.com/pure-admin/vue-pure-admin/commit/c83cb7d56be09ebb9ef6361a6a93bf025376f1d9))
- 修复平台设置弹窗取消功能异常，统一操作按钮样式 ([9e466e0](https://github.com/pure-admin/vue-pure-admin/commit/9e466e0a1a9609ca36a42c6bc25bcc8d30208414))
- 修复页面缓存 ([535a3b2](https://github.com/pure-admin/vue-pure-admin/commit/535a3b2abd84e493c9ddb3aabda04788c732a603))
- 修复重置密码 ([f14dac0](https://github.com/pure-admin/vue-pure-admin/commit/f14dac098f9c628c8a1f164417fc052c0c794097))
- 修复token冻结提示 ([3c40b92](https://github.com/pure-admin/vue-pure-admin/commit/3c40b9260e63140ec295e7373859ebdd5ce4b7b5))
- 修改角色管理账号管理删除按钮颜色 ([df4371c](https://github.com/pure-admin/vue-pure-admin/commit/df4371cb0a5d2cd4d40db276fd968a4aac497600))
- 修改提示词 ([4a7b028](https://github.com/pure-admin/vue-pure-admin/commit/4a7b02854202d8bf325ca9f04a0d64a428ee8395))
- 修改token冻结文案 ([5aed1ab](https://github.com/pure-admin/vue-pure-admin/commit/5aed1abccf49965670941ecad0f8f19d412a1b83))
- 学校数据UI调整 ([5a567b0](https://github.com/pure-admin/vue-pure-admin/commit/5a567b0431408ecc19d14ae62b9bf04913c3f9d2))
- 样式调整 ([606be58](https://github.com/pure-admin/vue-pure-admin/commit/606be58075041a73adac952b88a56d2188f66530))
- 页面调整 ([794a4de](https://github.com/pure-admin/vue-pure-admin/commit/794a4defab5591fda27a04bd494b71ff390bf0da))
- 页面刷新路由参数丢失(bug) ([6958473](https://github.com/pure-admin/vue-pure-admin/commit/69584731904cda15d5af7c013dd973591892173a))
- 重置按钮统一 ([776da00](https://github.com/pure-admin/vue-pure-admin/commit/776da00b14948bb3c4eb6cf215da0a07aa279651))
- 重置密码调整 ([bca8607](https://github.com/pure-admin/vue-pure-admin/commit/bca860745a91a10ff7109c330c845c296f8459a3))
- 专家库 批量异步导入 ([f766d98](https://github.com/pure-admin/vue-pure-admin/commit/f766d980938fdb0732ba5cd0ea4dca5c67981d66))
- 专家库(bug) ([b3bacad](https://github.com/pure-admin/vue-pure-admin/commit/b3bacad6b9c5d2d828e9e10e764a0b132ccd06c3))
- 专家库路由页面缓存 ([05e9a75](https://github.com/pure-admin/vue-pure-admin/commit/05e9a7575bc4601d04dec5b96fa469e422fb92d9))
- sm4空值检查 ([a5ba8fb](https://github.com/pure-admin/vue-pure-admin/commit/a5ba8fb1065ac24743807dc93ee1442dbd5e2782))

### [0.0.5](https://github.com/pure-admin/vue-pure-admin/compare/v0.0.4...v0.0.5) (2025-04-17)

### Bug Fixes

- 财务传参调整 ([dd2befd](https://github.com/pure-admin/vue-pure-admin/commit/dd2befd4abc83c84129233167148280d4d5c68b6))
- 轮播图替换走马灯 ([62dd6b2](https://github.com/pure-admin/vue-pure-admin/commit/62dd6b268f46d474f42ea5a202e37358bbd13c0b))
- 审核管理审核状态颜色区分及按钮颜色调整 ([631f921](https://github.com/pure-admin/vue-pure-admin/commit/631f921de112878286f6353effeb7d5897cb9f0e))
- 添加文件预览正式环境地址，机构管理页调整 ([74e3d10](https://github.com/pure-admin/vue-pure-admin/commit/74e3d1082ef4f2c8da8146c1cf4e461597e064f1))
- 作业设计图片 ([4ce693a](https://github.com/pure-admin/vue-pure-admin/commit/4ce693a7d9bb3f4a9485c39678752180a24757eb))

### [0.0.4](https://github.com/pure-admin/vue-pure-admin/compare/v0.0.3...v0.0.4) (2025-04-16)

### Bug Fixes

- 表格调整 ([e8ce4ad](https://github.com/pure-admin/vue-pure-admin/commit/e8ce4adaa514fe2974182b368e46935f36dfff03))
- 表格样式调整 ([5931030](https://github.com/pure-admin/vue-pure-admin/commit/593103062f278ddaab88fb30b6b775a26c7656da))
- 表格样式统一调整 ([c5fdb15](https://github.com/pure-admin/vue-pure-admin/commit/c5fdb158a07f3a430949e5b0da752e9cdad4494e))
- 订单页刷新 ([ee1a75a](https://github.com/pure-admin/vue-pure-admin/commit/ee1a75a3c5b1993abd9161af847047b13657cd4e))
- 首页编辑信息调整 ([a0b6e7d](https://github.com/pure-admin/vue-pure-admin/commit/a0b6e7d54301b0475f98a60f12d2532d6fdbf1d0))
- 首页面包屑调整 ([a5bd464](https://github.com/pure-admin/vue-pure-admin/commit/a5bd46469d8e990ed109500b0f4c35cf6c5a0150))
- 首页适配 ([94c2f12](https://github.com/pure-admin/vue-pure-admin/commit/94c2f12d64bc0b35b06afb4f4da3aa7abcbb6fe3))
- 添加上传预览组件 ([335129f](https://github.com/pure-admin/vue-pure-admin/commit/335129f2d257c51962f2f7e630383146e75cce8c))
- 添加code码提示 ([76c7ba9](https://github.com/pure-admin/vue-pure-admin/commit/76c7ba985625566d7ee74125fbfb483d1a9d4ae1))
- 图标调整 ([b326b9f](https://github.com/pure-admin/vue-pure-admin/commit/b326b9f037576ce89e63893a0b17583a4255d134))
- 文本超出 ([1f526e1](https://github.com/pure-admin/vue-pure-admin/commit/1f526e12b1da0f33570992a2cdb39e10de062bd3))
- 修复登录回车不触发图形验证 ([7c04a05](https://github.com/pure-admin/vue-pure-admin/commit/7c04a05b553759905aa81aceb452ce2ea134ac79))
- 页面调整 ([e7afc54](https://github.com/pure-admin/vue-pure-admin/commit/e7afc5452c5fabaf632f5e5dba724ff3283232f7))

### [0.0.3](https://github.com/pure-admin/vue-pure-admin/compare/v0.0.2...v0.0.3) (2025-04-15)

### Features

- 添加token异常码提示 ([a97a6ed](https://github.com/pure-admin/vue-pure-admin/commit/a97a6ed1f2cfbd2890b8e32a44b56c2b8cea5c79))

### Bug Fixes

- 调整面包屑展示、编辑信息机构隐藏、添加安全判断、列表页展示、同意样式 ([f61712a](https://github.com/pure-admin/vue-pure-admin/commit/f61712a797b32347fcb9ae0605dfc6a7b97baa9e))
- 分类管理 + 日志管理样式 ([fc48747](https://github.com/pure-admin/vue-pure-admin/commit/fc487472ca1103830cd95082eea6f8256fdb9965))
- 家长管理(bug) ([f2506ae](https://github.com/pure-admin/vue-pure-admin/commit/f2506aef1530059cba316970c643f473b3361a8f))
- 修复富文本无法上传图片 ([2235baa](https://github.com/pure-admin/vue-pure-admin/commit/2235baab768a5823128c2e160fdfb611b7b5f3ef))

### [0.0.2](https://github.com/pure-admin/vue-pure-admin/compare/v0.0.1...v0.0.2) (2025-04-15)

### 0.0.1 (2025-04-15)

### Features

- 创建机构新增一句话简介 ([3698515](https://github.com/pure-admin/vue-pure-admin/commit/369851507be3280869f94025b2ab6578a887602f))
- 当期详情页面 ([7062ce9](https://github.com/pure-admin/vue-pure-admin/commit/7062ce974abd63cb3d65e187f22f501b4268727b))
- 登录添加图形验证 ([530d595](https://github.com/pure-admin/vue-pure-admin/commit/530d59582e360dded020df3c1c9e91cc8f57453a))
- 调整权限 ([f542284](https://github.com/pure-admin/vue-pure-admin/commit/f5422840fdef9984ad9c8b2b7c97a2509c2a8dbd))
- 订单管理详情 ([cdf7df9](https://github.com/pure-admin/vue-pure-admin/commit/cdf7df9c93c552ce17e42efaa194eea94cf3fcdb))
- 分类管理页面 ([ee97ea6](https://github.com/pure-admin/vue-pure-admin/commit/ee97ea6b12742fc3dc2fa48142acc3c9cded19d8))
- 关联订单及各模块跳转课期详情处理 ([e6a7125](https://github.com/pure-admin/vue-pure-admin/commit/e6a71254f74d8eb04233f6ec0e7cf3cd8fd1b36d))
- 关联订单页面 ([c944463](https://github.com/pure-admin/vue-pure-admin/commit/c9444638abd1e8314a68ccd3e1c0829fbd345730))
- 家长管理+平台信息页面 ([53c33db](https://github.com/pure-admin/vue-pure-admin/commit/53c33db01775e1d78150569f31d8ff3c275090dc))
- 家长管理的订单管理+详情+子女详情+订单详情页面 ([9ceb763](https://github.com/pure-admin/vue-pure-admin/commit/9ceb7636f42264d4965ccb84c07c897cdd9f4646))
- 价格设置和作业设计 ([45e3122](https://github.com/pure-admin/vue-pure-admin/commit/45e3122da6fde79906c244b63ff951d46491b125))
- 局端管理员账号创建等 ([27bbfaf](https://github.com/pure-admin/vue-pure-admin/commit/27bbfaf2292ae53840c31a00d56dd177157ec644))
- 局端账号和平台账号校验 ([e05fe78](https://github.com/pure-admin/vue-pure-admin/commit/e05fe78e39fc58aba1a99d6169ee9b3aaed29419))
- 局端账号页面 ([051a387](https://github.com/pure-admin/vue-pure-admin/commit/051a3878599ab84ed2e6d979921121889c043edf))
- 课程报告及课程审核搜索 ([fc363d6](https://github.com/pure-admin/vue-pure-admin/commit/fc363d6e9d0017f8716d33400820e6c2ac091010))
- 课程及账号模块路由 ([a60b88d](https://github.com/pure-admin/vue-pure-admin/commit/a60b88d198a6af8b30ec923a9bc684239de9f1f3))
- 课程审核 ([9b12e5d](https://github.com/pure-admin/vue-pure-admin/commit/9b12e5dfef563cca451b92cf9cd237090bafbb32))
- 课程详情开发 ([c2915b8](https://github.com/pure-admin/vue-pure-admin/commit/c2915b8fbbfa52763c63e4aec674abc994598678))
- 课期基础信息及账号筛选 ([1d92943](https://github.com/pure-admin/vue-pure-admin/commit/1d9294303fc7d7a5647805d5dace144761f39353))
- 平台和机构页面建立 ([51b7635](https://github.com/pure-admin/vue-pure-admin/commit/51b7635419d45f075bbeee8fdcfd7331e4b902f6))
- 平台账号编辑 ([27c084d](https://github.com/pure-admin/vue-pure-admin/commit/27c084da4befe24c4ab45cdb581baf40579bdc7a))
- 平台账号创建 ([dc8932a](https://github.com/pure-admin/vue-pure-admin/commit/dc8932a4f1372e1216ee24ed315bd5e9bf703558))
- 上传列表展示 ([7899dcf](https://github.com/pure-admin/vue-pure-admin/commit/7899dcf19898a86daaadcbdb398d9ffcdf37d0b1))
- 上课跟踪和作业详情及评价详情 ([2fff10c](https://github.com/pure-admin/vue-pure-admin/commit/2fff10c33a19a0bd5d0fe9571e66316bd2995b60))
- 上下架接口 ([8e0af19](https://github.com/pure-admin/vue-pure-admin/commit/8e0af1911ac01dcd88e34785f3513017834d78f9))
- 审核页面及详情和局端平台账号首页开发 ([1b7e76a](https://github.com/pure-admin/vue-pure-admin/commit/1b7e76a4ea57c20d6b5e565ce94788c5b09f242d))
- 添加角色权限相关接口 ([701b835](https://github.com/pure-admin/vue-pure-admin/commit/701b8354037faeecad7c7195a222d6eb1f049aba))
- 添加领队讲师/详情课期及课程类型分类 ([5fb9400](https://github.com/pure-admin/vue-pure-admin/commit/5fb9400b7f60b99dd1d5b5ee2a989c205bc67b57))
- 添加埋点+新增首页编辑和修改密码 ([407a105](https://github.com/pure-admin/vue-pure-admin/commit/407a105a4d55ff26d6f49db7862c440bb38b8ac5))
- 添加权限码 ([d42f8ed](https://github.com/pure-admin/vue-pure-admin/commit/d42f8edd356ff44f0e86a0ebdf18d304dae9305e))
- 推荐课程页面 ([ea83def](https://github.com/pure-admin/vue-pure-admin/commit/ea83def4090e4808712254ecca655327ce723439))
- 完善机构管理页面 ([d559cca](https://github.com/pure-admin/vue-pure-admin/commit/d559ccaabb891bd34973987c3804289516534763))
- 文件上传相关文件 ([e31d604](https://github.com/pure-admin/vue-pure-admin/commit/e31d604568644dca4b8c2b7bd4ab54ae7407114a))
- 下载预览自定义指令 ([fb0d5ec](https://github.com/pure-admin/vue-pure-admin/commit/fb0d5ec44b20430c51bc4a33fb5ec12878008c58))
- 新增订单管理页 ([3aed81e](https://github.com/pure-admin/vue-pure-admin/commit/3aed81e3fc5d3d206739b8f0a86df56f5ce2b135))
- 新增基地管理静态页面 ([5c33c79](https://github.com/pure-admin/vue-pure-admin/commit/5c33c79042d4802d53f1d94c8f4919fcb36eee16))
- 新增基地和机构审核静态页面 ([e685675](https://github.com/pure-admin/vue-pure-admin/commit/e6856756f2e0571b5357b94fa943cca7e2d8d45b))
- 新增其他格式文件预览 ([461758e](https://github.com/pure-admin/vue-pure-admin/commit/461758ea48c48cfa934c77851f00f10b30cf01f6))
- 新增日志埋点功能+账务导出 ([be8a9df](https://github.com/pure-admin/vue-pure-admin/commit/be8a9dff5cc64ce819d0834d08df57f055140c2a))
- 新增首页 ([80882a6](https://github.com/pure-admin/vue-pure-admin/commit/80882a684dfccd848f92c5e12751f714d5ced49d))
- 学生管理+日志管理页面 ([2ce3ca2](https://github.com/pure-admin/vue-pure-admin/commit/2ce3ca2cdb5c70097ecbf4423cd2adcbda661c9e))
- 学生详情+ 平台轮播图UI修改 ([ba0d83c](https://github.com/pure-admin/vue-pure-admin/commit/ba0d83cfbd9d44fac59b7b50167e60535d223d3f))
- 用户评价及查看作业情况 ([d44ce02](https://github.com/pure-admin/vue-pure-admin/commit/d44ce02c6618462ab60c5421d458432b26050967))
- 增加富文本编辑器 ([4df7a16](https://github.com/pure-admin/vue-pure-admin/commit/4df7a1694947e700b3e3405a777751086d735479))
- 增加权限筛选 ([8b61fee](https://github.com/pure-admin/vue-pure-admin/commit/8b61fee68efc0f291313074b2727919d39b4567d))
- 增加图片icon支持 ([8b84409](https://github.com/pure-admin/vue-pure-admin/commit/8b8440982b7359c61e3a0d65c3e22bcce9314fe6))
- 账号创建 ([ee4500e](https://github.com/pure-admin/vue-pure-admin/commit/ee4500ee7be01ae5d9a4f83f45947863d16729be))
- **accountNumber:** 添加机构列 ([efd8af1](https://github.com/pure-admin/vue-pure-admin/commit/efd8af14c127d7a5fcee0166db17ad155c16b0d0))
- **accountNumber:** 添加领队页面、列表、搜索、冻结/解冻功能、添加领队详情、资质文件、编辑信息 ([8692c37](https://github.com/pure-admin/vue-pure-admin/commit/8692c37de51c6478a9515bf03566843162291256))
- **accountNumber:** 优化账号管理功能,添加手机号验证和获取验证码功能,实现自定义手机号校验规则,增加二维码生成和显示 ([d00c002](https://github.com/pure-admin/vue-pure-admin/commit/d00c002ef2249b4455b88f83bf268d6ce7e69762))
- **api:** 更新课程周期查询接口路径 ([f075938](https://github.com/pure-admin/vue-pure-admin/commit/f075938a284aa2df62c6e4f0a153056c1d252f63))
- **lecturerManagement:** 添加讲师管理功能 ([0d6358a](https://github.com/pure-admin/vue-pure-admin/commit/0d6358a38567bc0281f61d2f1e54c7eeca6c6fb6))

### Bug Fixes

- 路由面包屑刷新（bug） ([b51d90e](https://github.com/pure-admin/vue-pure-admin/commit/b51d90efe7e2c7abb4de712c8c3416e04fdde4dd))
- 本地存储用户信息 ([71de9af](https://github.com/pure-admin/vue-pure-admin/commit/71de9afc3d34a50b4e68bfb70c4de319eaa67e67))
- 侧边栏图标 ([41f63ff](https://github.com/pure-admin/vue-pure-admin/commit/41f63ffb9832197034ef097bf0d8fbd6badac895))
- 侧边栏图标 ([ca55aaf](https://github.com/pure-admin/vue-pure-admin/commit/ca55aafb6400210c8cf08f567fa82cf614874db5))
- 侧边栏图标 ([69de2df](https://github.com/pure-admin/vue-pure-admin/commit/69de2df21799661ca7b924d879d0808cd0c4d45a))
- 创建机构初始密码调整 ([9451e0b](https://github.com/pure-admin/vue-pure-admin/commit/9451e0bdaab17a16fd2dfee6563583c035ff11bf))
- 登录调整 ([e70ef6e](https://github.com/pure-admin/vue-pure-admin/commit/e70ef6e934bc1f3e33c431c83e6fcca4526cad7b))
- 登录调整及手机号证件号明文与加密显示 ([6e7f733](https://github.com/pure-admin/vue-pure-admin/commit/6e7f7335ea61f24df84e8094229371017ef53e13))
- 登录接口调整 ([af7ff74](https://github.com/pure-admin/vue-pure-admin/commit/af7ff74457817badf024f43ccef2090e4e8ec580))
- 登录时取消空格 ([f1e9502](https://github.com/pure-admin/vue-pure-admin/commit/f1e95026213d9a4542abf68852195ce0dfdb3bce))
- 登录页调整 ([c1cc3a0](https://github.com/pure-admin/vue-pure-admin/commit/c1cc3a0f1b02ab9824a222bfea38b340dccb82cc))
- 调整登录权限 ([aacd355](https://github.com/pure-admin/vue-pure-admin/commit/aacd3558469bb1ca4ba2e77baabaae077b630e56))
- 调整登录文案 ([2815e6d](https://github.com/pure-admin/vue-pure-admin/commit/2815e6d923f10fe2420357a872613bae0d9c9448))
- 调整平台设置文件上传 ([31f527b](https://github.com/pure-admin/vue-pure-admin/commit/31f527b09fb917f59bde17086fe2444fa29294d3))
- 调整文件预览 ([c93a22d](https://github.com/pure-admin/vue-pure-admin/commit/c93a22d3024f0f4e40809c3f3eea41cdb2912325))
- 调整异常状态提示 ([cc3f9bf](https://github.com/pure-admin/vue-pure-admin/commit/cc3f9bff4adb0d031b6dc3d4fea03d8ac66fe40d))
- 调整code码 ([ac09540](https://github.com/pure-admin/vue-pure-admin/commit/ac09540633af72018e16d92d0162e9b6b007a879))
- 订单管理路由+样式调整 ([f60cc3e](https://github.com/pure-admin/vue-pure-admin/commit/f60cc3e4fe84424fa203b37534c16bc49cc9afe9))
- 订单管理页调整 ([4fdc5d8](https://github.com/pure-admin/vue-pure-admin/commit/4fdc5d810e30c865ee5357bd3ab74e57af0091ce))
- 订单详情+首页调整 ([ee83c8d](https://github.com/pure-admin/vue-pure-admin/commit/ee83c8deccb86e221db567800bf789319242a950))
- 订单详情调整 ([963c272](https://github.com/pure-admin/vue-pure-admin/commit/963c27249b156763e831124a2dac05a2e08ccb69))
- 订单详情调整+首页样式调整 ([7937ad6](https://github.com/pure-admin/vue-pure-admin/commit/7937ad62cccbed02843861bb4855d69dfcb181fb))
- 订单详情页调整 ([1ce746d](https://github.com/pure-admin/vue-pure-admin/commit/1ce746dc7072af93d5574bfca500dc147886448e))
- 订单页调整 ([712966f](https://github.com/pure-admin/vue-pure-admin/commit/712966fd5931b96216ff84f0851292ee15f32fb5))
- 多端登录退出异常 ([9bb8901](https://github.com/pure-admin/vue-pure-admin/commit/9bb89010e5f6572066886a2bb2c1443ee1694997))
- 分类管理 ([2781c46](https://github.com/pure-admin/vue-pure-admin/commit/2781c46e2bcfaff324214dde9a6de903783469df))
- 分类管理 ([a809fa9](https://github.com/pure-admin/vue-pure-admin/commit/a809fa94b6ef891a80f583fdf7ddd79c0853be2f))
- 分类管理(编辑bug) ([53d5604](https://github.com/pure-admin/vue-pure-admin/commit/53d56048ac253e34299731c8cede440de91258da))
- 分类管理弹框+学校数据+平台信息UI调整 ([da64c1f](https://github.com/pure-admin/vue-pure-admin/commit/da64c1f6954ca9e317cad459dd3ccd76c79c99c3))
- 分类管理改多级分类 ([5bc28c8](https://github.com/pure-admin/vue-pure-admin/commit/5bc28c8ad74526e6c08c056bc4a3d7d286ee4f8f))
- 分类管理链接接口 ([17fd6ab](https://github.com/pure-admin/vue-pure-admin/commit/17fd6abf837cc2112b8d834bff46b7c568d4228e))
- 分类管理埋点 + 家长\学生的详情跳转 ([edd9426](https://github.com/pure-admin/vue-pure-admin/commit/edd9426690ed365bfa2c49d30df0e5a217fa614c))
- 分类管理面包屑UI ([e0fb8b9](https://github.com/pure-admin/vue-pure-admin/commit/e0fb8b987c7aa3c7a9c383773748558dd3f29193))
- 分类管理添加编辑按钮 ([11c367b](https://github.com/pure-admin/vue-pure-admin/commit/11c367b1fdb7b5309c4db290573f78f5d7117ad1))
- 分类管理UI调整+轮播图UI调整 ([f34b2c2](https://github.com/pure-admin/vue-pure-admin/commit/f34b2c2962a9fdb72c85c81dcc0dcc694b01c11e))
- 分类名修改接口+家长管理中的退单接口 ([f9ae163](https://github.com/pure-admin/vue-pure-admin/commit/f9ae16341e1f1799ef7fa754d95ec25a6ca25fb7))
- 分页数据修改 ([0fd5369](https://github.com/pure-admin/vue-pure-admin/commit/0fd536958663364e014214864ccab82a632baf86))
- 功能调整+UI ([a76c5cb](https://github.com/pure-admin/vue-pure-admin/commit/a76c5cb2e679b0811d92241509fd74c0c26cf4fd))
- 关联订单 ([52415eb](https://github.com/pure-admin/vue-pure-admin/commit/52415ebb349c3ef63e09598869626b2652fa11e8))
- 机构管理+基地管理 路由调整 ([e4a865e](https://github.com/pure-admin/vue-pure-admin/commit/e4a865e98fea92d0dd84351be12c976a1addd9cb))
- 机构管理+基地管理(修改) ([df86d1c](https://github.com/pure-admin/vue-pure-admin/commit/df86d1c717efe16a07ea5c8391e87d6a71d88752))
- 机构管理条件搜索bug ([7cbf5cd](https://github.com/pure-admin/vue-pure-admin/commit/7cbf5cd0b5f407c346e26448b99e2de724c7e48b))
- 基本信息样式调整 ([f87737b](https://github.com/pure-admin/vue-pure-admin/commit/f87737b8784435c8e69ced8fa865d4d630d8d214))
- 基地管理UI调整 ([2a815c9](https://github.com/pure-admin/vue-pure-admin/commit/2a815c9d9c6d8a18d42dff3ad3760da6d4d5dbc0))
- 家长-订单管理和详情中的订单状态调整 ([6cd72b8](https://github.com/pure-admin/vue-pure-admin/commit/6cd72b8ff1fa55b3e822937df95f04cff4269f87))
- 家长管理-订单管理接口 ([bca1d59](https://github.com/pure-admin/vue-pure-admin/commit/bca1d596f1f242e333ef9120eb25159fa50236eb))
- 家长管理接口 ([9f9ed1d](https://github.com/pure-admin/vue-pure-admin/commit/9f9ed1dd4e8d397691c705f6d68d28af86d20b4d))
- 家长管理接口2.0 ([7394a66](https://github.com/pure-admin/vue-pure-admin/commit/7394a661b4f6516f4c99a8facade5bdf77308780))
- 家长管理接口3.0 ([2cd7d89](https://github.com/pure-admin/vue-pure-admin/commit/2cd7d898a4aa3fb92393602099f5dae9656e6bce))
- 家长管理接口4.0 ([32f5684](https://github.com/pure-admin/vue-pure-admin/commit/32f5684f8531cd13aba97922445fb0035274c49e))
- 家长管理接口调整+平台轮播图UI调整 ([3fd1379](https://github.com/pure-admin/vue-pure-admin/commit/3fd1379303e839c83bf6b2526a12e64ba8db9fd2))
- 家长管理手机证件icon脱敏 ([c47c609](https://github.com/pure-admin/vue-pure-admin/commit/c47c60906f150850d0deac906fdaec9d3d9a1750))
- 家长管理UI调整 ([dbe1c2b](https://github.com/pure-admin/vue-pure-admin/commit/dbe1c2b1068434a4858a99335c5df99aebf6c111))
- 家长子女详情证件号脱敏 ([92e9fa6](https://github.com/pure-admin/vue-pure-admin/commit/92e9fa6daad3f61b738079d4c4b3b2200ef6cda2))
- 局端账号和平台账号的重置密码 ([6f30e94](https://github.com/pure-admin/vue-pure-admin/commit/6f30e94ea310f0eee5b24f96119203c34afec04e))
- 局端账号和平台账号重置密码弹框调整 ([95de2df](https://github.com/pure-admin/vue-pure-admin/commit/95de2df9eecc485b4eeb97a053e289236b893d5e))
- 开启权限功能 ([c8b558c](https://github.com/pure-admin/vue-pure-admin/commit/c8b558c82e12817c0569ca1a6faff1c5f6f2bd15))
- 课程分类级联选择 ([c04d774](https://github.com/pure-admin/vue-pure-admin/commit/c04d774828834f030a7b5aaa6a88252dd241b18e))
- 课程管理搜索及清除问题 ([76a68af](https://github.com/pure-admin/vue-pure-admin/commit/76a68af951961531f703cfcc01a3ffa5558476fd))
- 课程列表调整 ([bcfb2fe](https://github.com/pure-admin/vue-pure-admin/commit/bcfb2fe2a6f2f915cb485c46cd6b207ff32abd9c))
- 课程审核加loading状态 ([7ec3f68](https://github.com/pure-admin/vue-pure-admin/commit/7ec3f680ec24d120863488c9a51306c2747469c0))
- 课程详情及课期列表调整 ([e8a61ca](https://github.com/pure-admin/vue-pure-admin/commit/e8a61ca78a820c1b02967b738f5f33b7346da08c))
- 课程详情跳转 ([db069e1](https://github.com/pure-admin/vue-pure-admin/commit/db069e10f349c28dee6d459986cb2f34ea95d4b1))
- 课程详情详细资料调整 ([c258de4](https://github.com/pure-admin/vue-pure-admin/commit/c258de4066317f4e3bbbc3efb1660f257e140e46))
- 课程账号路由和初始密码修改 ([64553db](https://github.com/pure-admin/vue-pure-admin/commit/64553db151df82867b6a6e26640926aaffa32d12))
- 课期详情基本信息调整 ([0f2f9ba](https://github.com/pure-admin/vue-pure-admin/commit/0f2f9ba2998859f1e838e165393e2867045cb224))
- 路由 ([7839fb5](https://github.com/pure-admin/vue-pure-admin/commit/7839fb501230d9ffc0c21eef21acfb0a0f4b8c7a))
- 路由+平台图标居中且不带框 ([a1bfa6e](https://github.com/pure-admin/vue-pure-admin/commit/a1bfa6e6300cc42c26db9b64d341cd166021a0c8))
- 路由问题调整 ([5e8b97b](https://github.com/pure-admin/vue-pure-admin/commit/5e8b97b0fdcad81b2b42b2a1d2ec015a2639fa51))
- 轮播图接口数据调整 ([e014748](https://github.com/pure-admin/vue-pure-admin/commit/e0147487c9447693621be2c6a3e6018999c6e318))
- 轮播图课程优化 ([ae71011](https://github.com/pure-admin/vue-pure-admin/commit/ae71011e52d3841afa321a4207d55ee7d31257d6))
- 密文账号编辑调整 ([59f6969](https://github.com/pure-admin/vue-pure-admin/commit/59f6969db11ae39c3a18aa1bcc3bb29dc6662846))
- 面包屑（title） ([dddf6a5](https://github.com/pure-admin/vue-pure-admin/commit/dddf6a52932f75f927b717f86963141af9aa6204))
- 平台局端账号日志 ([81b4d9d](https://github.com/pure-admin/vue-pure-admin/commit/81b4d9d0edac85bab24b88a62e822200282b8d9c))
- 平台轮播图接口接入即调整 ([5b48b11](https://github.com/pure-admin/vue-pure-admin/commit/5b48b11e222eb4bcf2879e48fc4e7b2600505847))
- 平台平台信息+推荐课程接口接入即调整 ([7af0285](https://github.com/pure-admin/vue-pure-admin/commit/7af02853c518ee3ebc7b5511ee09f5ba57f973dc))
- 平台热门课程功能 ([39e1587](https://github.com/pure-admin/vue-pure-admin/commit/39e158704566b90def1bdc720a2ea9153f260e83))
- 平台设置 ([a732209](https://github.com/pure-admin/vue-pure-admin/commit/a7322090b4636070c36fda206068e237b17cf313))
- 平台设置调整 ([d2c9692](https://github.com/pure-admin/vue-pure-admin/commit/d2c9692d5f72f153868659765e048e577b3025d4))
- 平台设置调整 ([c5fe33f](https://github.com/pure-admin/vue-pure-admin/commit/c5fe33f1766fce9907f2ed352513ae6fbe9b4718))
- 平台设置调整 ([dbef74c](https://github.com/pure-admin/vue-pure-admin/commit/dbef74cbb161e9cd30f03535e7b256f74c7a82b3))
- 平台设置轮播图、热门课程、精品课程、弹框UI调整 ([cc457fc](https://github.com/pure-admin/vue-pure-admin/commit/cc457fcc3b233983d237a5e7097c08da15c9619d))
- 平台设置埋点 + 家长管理埋点 +家长管理的订单管理和详情 ([7c66b44](https://github.com/pure-admin/vue-pure-admin/commit/7c66b44ee88b6e366f6f6cafcb7188328ae7da0d))
- 平台推荐课程UI调整 ([28a64af](https://github.com/pure-admin/vue-pure-admin/commit/28a64afda68cfe1b4272752c0f15cb008783211a))
- 平台信息优化 ([58dd668](https://github.com/pure-admin/vue-pure-admin/commit/58dd66813f305d8f7c68d299249aa2e3e7b89266))
- 平台账号及局端账号筛选问题及角色选择 ([d5dc9e7](https://github.com/pure-admin/vue-pure-admin/commit/d5dc9e7b4e681f253924ed18a17c7d2aa74b324c))
- 签名调整 ([e8eb95a](https://github.com/pure-admin/vue-pure-admin/commit/e8eb95a0e77ce9fd6841574dddf7852a8e792fd4))
- 全部评价调整 ([801f4c9](https://github.com/pure-admin/vue-pure-admin/commit/801f4c905bc13a6959cd9e545612289507dca8d7))
- 日志管理UI调整 ([e50656f](https://github.com/pure-admin/vue-pure-admin/commit/e50656fffc07c425ea84406d930933d4f960015f))
- 删除多余dialog ([3607a2d](https://github.com/pure-admin/vue-pure-admin/commit/3607a2d982c13a4e059161e2353b0519c34e299f))
- 删除上架等按钮颜色调整 ([b74d7f3](https://github.com/pure-admin/vue-pure-admin/commit/b74d7f3320adefeae689b4fa15d5cf50499c24fe))
- 上课跟踪调整 ([a8a3466](https://github.com/pure-admin/vue-pure-admin/commit/a8a3466f3f56691bf290d38c5e519c3236f5c464))
- 时间选择器默认时间(bug) ([942fa0a](https://github.com/pure-admin/vue-pure-admin/commit/942fa0a4914cc3240693352fe98cde02c4f827d1))
- 手机号+证件脱敏 ([ec79c77](https://github.com/pure-admin/vue-pure-admin/commit/ec79c777c6777c8bd5ea82f23d6beebca24e848e))
- 手机号正则 ([265d1cd](https://github.com/pure-admin/vue-pure-admin/commit/265d1cdc8c94d8e3f57fc186ac64c9248b913c41))
- 首页字体修改 ([b6d1676](https://github.com/pure-admin/vue-pure-admin/commit/b6d16765085bf94fae439888e262f447ae384f9e))
- 输入框不可编辑 ([a8069e2](https://github.com/pure-admin/vue-pure-admin/commit/a8069e2577c117da582d27e94f18378f05b2b653))
- 思政平台标题文案 ([c354a1b](https://github.com/pure-admin/vue-pure-admin/commit/c354a1b6677eb1e1d62b08cb137bda085ff27907))
- 提示修改 ([b30e38a](https://github.com/pure-admin/vue-pure-admin/commit/b30e38acc5c4dab8f59c389b4e9f35dfc23c57dc))
- 条件渲染当期详情表格内容 ([f2e7c40](https://github.com/pure-admin/vue-pure-admin/commit/f2e7c4034c9444a80f5ee5d48de62e6bf9892238))
- 图片回显+文本居中 ([9109045](https://github.com/pure-admin/vue-pure-admin/commit/910904597582a7c14a6417dbd37c23ac1bdb63b8))
- 图片展示问题 ([7e2e168](https://github.com/pure-admin/vue-pure-admin/commit/7e2e168adb3d0acf4759ba6d2b5924e73749408f))
- 推荐课程接口数据调整 ([bfee518](https://github.com/pure-admin/vue-pure-admin/commit/bfee518cb0c3a20ead2acfc9445ebc0f9cb97776))
- 新增机构管理创建机构 ([03d0b31](https://github.com/pure-admin/vue-pure-admin/commit/03d0b316cfb7b5b56d4f08707932a79d384ab33d))
- 行程安排显示 ([3f7f116](https://github.com/pure-admin/vue-pure-admin/commit/3f7f116782bdfcf0862dfdfeb773207082171f42))
- 修复领队讲师手机号验证 ([a09a8e7](https://github.com/pure-admin/vue-pure-admin/commit/a09a8e7ac44335f0bceb1545f891667f97d6db74))
- 修复提示，取消验证码必传 ([3feda2c](https://github.com/pure-admin/vue-pure-admin/commit/3feda2cffd2e424524eadc2bcf3d7e9e116a904e))
- 修改文件预览地址+机构管理页面调整 ([a2f64c2](https://github.com/pure-admin/vue-pure-admin/commit/a2f64c200443e7de023170cd5135c099f32c2da6))
- 修改token名，防止重复 ([087e3fa](https://github.com/pure-admin/vue-pure-admin/commit/087e3fab4bd2eaa098fae5e0abd0a43eb515068b))
- 学生\家长管理表格样式调整 ([29563a6](https://github.com/pure-admin/vue-pure-admin/commit/29563a63264bba9e9dc36ac514eefc06dbf95b64))
- 学生+家长+机构子路由调整 ([3f1a14d](https://github.com/pure-admin/vue-pure-admin/commit/3f1a14da9168205e7a71761282bb211d8e0c8db2))
- 学生管理接口接入 ([f5d901f](https://github.com/pure-admin/vue-pure-admin/commit/f5d901fb51a66ff531a61866cb1b0a870c145684))
- 学生情况跳转+局端和平台账号的密码重置 ([f32a644](https://github.com/pure-admin/vue-pure-admin/commit/f32a64446050017bcfdb54bf961e01a5f0d2a558))
- 学校数据+关联子女接口即调试 ([99864b3](https://github.com/pure-admin/vue-pure-admin/commit/99864b3274c9990893b6b96e5e44026c559d82e3))
- 样式调整+机构管理重置密码成功后不退出登录 ([66e89d3](https://github.com/pure-admin/vue-pure-admin/commit/66e89d39d45fbe2e9b5656a58ce3b3236afcf505))
- 页面调整 ([e7a0900](https://github.com/pure-admin/vue-pure-admin/commit/e7a0900883842d152d411d0e0bc7b3a775f19bf5))
- 页面调整 ([a115383](https://github.com/pure-admin/vue-pure-admin/commit/a115383565b45771c62a1f4821007e8a54c9a895))
- 页面对齐+推荐课程添加上移或下移接口 ([8d96f8c](https://github.com/pure-admin/vue-pure-admin/commit/8d96f8cb271c79551b2d7f2aa805f2fdbf4cad89))
- 隐藏侧边栏 ([6f313f0](https://github.com/pure-admin/vue-pure-admin/commit/6f313f07b7c3a235cee52a35e0e441ba04fd800e))
- 隐藏系统默认角色删除 ([530256b](https://github.com/pure-admin/vue-pure-admin/commit/530256ba4b9b31ab80990c0fe6ad7a22273d4d91))
- 隐藏账户设置 ([2f9296e](https://github.com/pure-admin/vue-pure-admin/commit/2f9296e57a4e71be27fd7f632393056433c3eb6f))
- 隐藏HideTabs ([35f7765](https://github.com/pure-admin/vue-pure-admin/commit/35f776558bd6aeb64f21c99da82ce6d2c76a7d6e))
- 用户评价和全部评价调整 ([ba68863](https://github.com/pure-admin/vue-pure-admin/commit/ba68863940fc12b34d304ec931594276aaf5093e))
- 用户评价及学生情况 ([670b177](https://github.com/pure-admin/vue-pure-admin/commit/670b177e0d054865e9d79e8c14bc9fa27dcf5691))
- 用户数据存储 ([2b6ce31](https://github.com/pure-admin/vue-pure-admin/commit/2b6ce313c071dd6e4b3ad58c881264ba4354cd38))
- 账号编辑按钮调整 ([4154226](https://github.com/pure-admin/vue-pure-admin/commit/4154226af1e156619d05a18aa6213a774cf3ede7))
- 重置密码调整 ([2332bd6](https://github.com/pure-admin/vue-pure-admin/commit/2332bd6df88a7e8764aec3e75e7cc4d4cccff4c6))
- 重置密码调整+空状态调整 ([a86e10c](https://github.com/pure-admin/vue-pure-admin/commit/a86e10c7d67eb9ef5e0e7f8d4ccba03e34a300fb))
- 子女详情跳转+密码重置 ([82b0d19](https://github.com/pure-admin/vue-pure-admin/commit/82b0d1912f4d58b4ce7a21d22e95f1521da01de2))
- add role and auth pages ([d699129](https://github.com/pure-admin/vue-pure-admin/commit/d6991294823fcb015c2b6a36f4552e5ee2a00d6e))
- bug修改 ([c1cd3d4](https://github.com/pure-admin/vue-pure-admin/commit/c1cd3d4a01aa751c93d9d0528a1fdecf31ce2f3e))
- butt调整 ([90c1989](https://github.com/pure-admin/vue-pure-admin/commit/90c1989b1100e44d7f1574fe44af279337207f71))
- html文件语言调整 ([d2ec67b](https://github.com/pure-admin/vue-pure-admin/commit/d2ec67b3247bc03c1dc353814fc573a361f89403))
- token调整 ([0e0f0b6](https://github.com/pure-admin/vue-pure-admin/commit/0e0f0b65192acf08ee15846145fda0dd2725b88a))
