<script setup>
import { ref, reactive, onMounted, h } from "vue";
import { useRouter } from "vue-router";
import { PlusSearch } from "plus-pro-components";
import { ElMessage, ElMessageBox } from "element-plus";

defineOptions({
  name: "CouponManagementIndex"
});

const router = useRouter();

// 搜索表单数据
const searchForm = ref({
  couponName: "",
  receiveTimeRange: [],
  useTimeRange: [],
  status: ""
});

// Tab栏配置
const activeTab = ref(0);
const tabOptions = ref([
  { name: "有效", value: "valid" },
  { name: "已失效", value: "invalid" }
]);

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 搜索配置
const searchColumns = ref([
  {
    label: "优惠券名称",
    prop: "couponName"
  },
  {
    label: "领取时间",
    prop: "receiveTimeRange",
    valueType: "date-picker",
    fieldProps: {
      type: "daterange",
      startPlaceholder: "开始时间",
      endPlaceholder: "结束时间"
    }
  },
  {
    label: "使用时间",
    prop: "useTimeRange",
    valueType: "date-picker",
    fieldProps: {
      type: "daterange",
      startPlaceholder: "开始时间",
      endPlaceholder: "结束时间"
    }
  },
  {
    label: "状态",
    prop: "status",
    valueType: "select",
    options: [
      { label: "全部", value: "" },
      { label: "启用", value: "enabled" },
      { label: "停用", value: "disabled" }
    ]
  }
]);

// 表格列配置
const columns = ref([
  {
    label: "优惠券名",
    prop: "couponName",
    minWidth: 120
  },
  {
    label: "发放时间（开始）",
    prop: "issueStartTime",
    minWidth: 120
  },
  {
    label: "使用时间",
    prop: "useTime",
    minWidth: 150
  },
  {
    label: "领取数量/发放数量",
    prop: "receiveCount",
    minWidth: 140
  },
  {
    label: "优惠规则",
    prop: "discountRule",
    minWidth: 120
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80,
    cellRenderer: ({ row }) => {
      const statusMap = {
        enabled: { text: "启用", type: "success" },
        disabled: { text: "停用", type: "danger" }
      };
      const status = statusMap[row.status] || { text: "未知", type: "info" };
      return h("el-tag", { type: status.type }, status.text);
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 260,
    slot: "operation"
  }
]);

// Mock数据
const mockData = [
  {
    id: 1,
    couponName: "新用户专享",
    issueStartTime: "2025-04-11",
    useTime: "2025-04-21 至 2025-04-21",
    receiveCount: "150/300 (50%)",
    discountRule: "满100减50",
    type: "通用",
    status: "enabled",
    validity: "valid" // 有效
  },
  {
    id: 2,
    couponName: "老客户回归",
    issueStartTime: "2025-04-11",
    useTime: "2025-04-21 至 2025-04-21",
    receiveCount: "300/300 (100%)",
    discountRule: "满100减50",
    type: "适用",
    status: "enabled",
    validity: "valid" // 有效
  },
  {
    id: 3,
    couponName: "轻松一夏",
    issueStartTime: "2025-04-11",
    useTime: "2025-04-21 至 2025-04-21",
    receiveCount: "150/300 (50%)",
    discountRule: "满100减10",
    type: "适用",
    status: "enabled",
    validity: "valid" // 有效
  },
  {
    id: 4,
    couponName: "快乐一起学",
    issueStartTime: "2025-04-11",
    useTime: "2025-04-21 至 2025-04-21",
    receiveCount: "150/300 (50%)",
    discountRule: "满100减50",
    type: "材料",
    status: "disabled",
    validity: "valid" // 有效
  },
  {
    id: 5,
    couponName: "利好5",
    issueStartTime: "2025-04-11",
    useTime: "2025-04-21 至 2025-04-21",
    receiveCount: "0/800 (0%)",
    discountRule: "满100减50",
    type: "材料",
    status: "enabled",
    validity: "valid" // 有效
  },
  {
    id: 6,
    couponName: "春节特惠",
    issueStartTime: "2024-12-01",
    useTime: "2024-12-31 至 2025-01-31",
    receiveCount: "500/500 (100%)",
    discountRule: "满200减100",
    type: "通用",
    status: "disabled",
    validity: "invalid" // 已失效
  },
  {
    id: 7,
    couponName: "双十一狂欢",
    issueStartTime: "2024-11-01",
    useTime: "2024-11-11 至 2024-11-20",
    receiveCount: "800/1000 (80%)",
    discountRule: "满300减150",
    type: "适用",
    status: "disabled",
    validity: "invalid" // 已失效
  },
  {
    id: 8,
    couponName: "暑期特训",
    issueStartTime: "2024-06-01",
    useTime: "2024-07-01 至 2024-08-31",
    receiveCount: "200/300 (67%)",
    discountRule: "满150减75",
    type: "材料",
    status: "disabled",
    validity: "invalid" // 已失效
  }
];

// 获取当前tab对应的数据
const getCurrentTabData = () => {
  const currentTabValue = tabOptions.value[activeTab.value]?.value;
  return mockData.filter(item => item.validity === currentTabValue);
};

// 加载表格数据
const loadTableData = () => {
  loading.value = true;
  setTimeout(() => {
    const filteredData = getCurrentTabData();
    tableData.value = filteredData;
    pagination.total = filteredData.length;
    loading.value = false;
  }, 500);
};

// Tab切换处理
const handleTabClick = tab => {
  activeTab.value = tab.props ? tab.props.name : tab.index;
  pagination.currentPage = 1;
  loadTableData();
};

// 手动派发相关数据
const manualDispatchVisible = ref(false);
const confirmDispatchVisible = ref(false);
const currentCoupon = ref({});
const dispatchForm = ref({
  userAccount: ""
});

// 表单验证规则
const dispatchRules = reactive({
  userAccount: [{ required: true, message: "请输入用户账号", trigger: "blur" }]
});

const dispatchFormRef = ref();

// 搜索处理
const handleSearch = values => {
  console.log("搜索参数:", values);
  searchForm.value = { ...values };
  pagination.currentPage = 1;
  loadTableData();
};

// 重置处理
const handleReset = () => {
  console.log("重置搜索");
  loadTableData();
};

// 分页处理
const handleSizeChange = size => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  loadTableData();
};

const handleCurrentChange = page => {
  pagination.currentPage = page;
  loadTableData();
};

// 新建优惠券
const handleCreateCoupon = () => {
  router.push({
    path: "/coupon/management/create",
    query: {
      type: "create"
    }
  });
};

// 操作处理函数
const handleDetail = row => {
  router.push({
    path: "/coupon/management/detail",
    query: {
      id: row.id
    }
  });
};

const handleToggleStatus = row => {
  const action = row.status === "enabled" ? "停用" : "启用";
  ElMessageBox.confirm(
    `确定要${action}优惠券"${row.couponName}"吗？`,
    "确认操作",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  )
    .then(() => {
      row.status = row.status === "enabled" ? "disabled" : "enabled";
      ElMessage.success(`${action}成功`);
    })
    .catch(() => {
      ElMessage.info("已取消操作");
    });
};

const handleCopy = row => {
  // 这里应该调用接口获取优惠券详情，但后端未实现，所以直接跳转
  ElMessage.success(`正在复制优惠券: ${row.couponName}`);
  router.push({
    path: "/coupon/management/create",
    query: {
      type: "copy",
      id: row.id
    }
  });
};

// 手动派发优惠券
const handleManualDispatch = row => {
  currentCoupon.value = row;
  dispatchForm.value.userAccount = "";
  manualDispatchVisible.value = true;
};

// 确认派发
const handleConfirmDispatch = () => {
  dispatchFormRef.value.validate(valid => {
    if (valid) {
      manualDispatchVisible.value = false;
      confirmDispatchVisible.value = true;
    }
  });
};

// 最终确认派发
const handleFinalConfirm = () => {
  // 这里应该调用后端接口进行派发，但后端未实现
  ElMessage.success(
    `优惠券 "${currentCoupon.value.couponName}" 已成功派发给用户 "${dispatchForm.value.userAccount}"`
  );
  confirmDispatchVisible.value = false;
  // 重新加载数据
  loadTableData();
};

// 取消派发
const handleCancelDispatch = () => {
  manualDispatchVisible.value = false;
  dispatchForm.value.userAccount = "";
};

// 取消二次确认
const handleCancelConfirm = () => {
  confirmDispatchVisible.value = false;
};

// 初始化
onMounted(() => {
  loadTableData();
});
</script>

<template>
  <div class="coupon-management">
    <!-- 搜索区域 -->
    <div class="common">
      <div class="search-section">
        <PlusSearch
          v-model="searchForm"
          :columns="searchColumns"
          :show-number="6"
          :has-unfold="false"
          label-width="120px"
          @search="handleSearch"
          @reset="handleReset"
        />
      </div>
    </div>

    <!-- Tab栏和操作按钮 -->
    <div class="common">
      <!-- Tab栏和按钮在同一行 -->
      <div class="tab-button-row">
        <div class="coupon-tabs">
          <el-tabs
            v-model="activeTab"
            class="demo-tabs"
            @tab-click="handleTabClick"
          >
            <el-tab-pane
              v-for="(item, index) in tabOptions"
              :key="index"
              :label="item.name"
              :name="index"
            />
          </el-tabs>
        </div>

        <div class="button-section">
          <el-button type="primary" @click="handleCreateCoupon">
            新建优惠券
          </el-button>
        </div>
      </div>

      <div class="table-section">
        <pure-table
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :data="tableData"
          :columns="columns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <div class="operation-buttons">
              <el-button
                class="reset-margin"
                link
                type="primary"
                @click="handleDetail(row)"
              >
                详情
              </el-button>
              <el-button
                class="reset-margin"
                link
                type="primary"
                @click="handleManualDispatch(row)"
              >
                手动派发
              </el-button>
              <el-button
                class="reset-margin"
                link
                :type="row.status === 'enabled' ? 'danger' : 'success'"
                @click="handleToggleStatus(row)"
              >
                {{ row.status === "enabled" ? "停用" : "启用" }}
              </el-button>
              <el-button
                class="reset-margin"
                link
                type="primary"
                @click="handleCopy(row)"
              >
                复制
              </el-button>
            </div>
          </template>
        </pure-table>
      </div>
    </div>

    <!-- 手动派发弹窗 -->
    <el-dialog
      v-model="manualDispatchVisible"
      title="手动派发"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="dispatch-info">
        <div class="info-item">
          <span class="label">优惠券名称：</span>
          <span class="value">{{ currentCoupon.couponName }}</span>
        </div>
        <div class="info-item">
          <span class="label">优惠券使用类型：</span>
          <span class="value">{{ currentCoupon.type }}</span>
        </div>
        <div class="info-item">
          <span class="label">优惠门槛与金额：</span>
          <span class="value">{{ currentCoupon.discountRule }}</span>
        </div>
        <div class="info-item">
          <span class="label">使用时间：</span>
          <span class="value">{{ currentCoupon.useTime }}</span>
        </div>
      </div>

      <el-form
        ref="dispatchFormRef"
        :model="dispatchForm"
        :rules="dispatchRules"
        label-width="0"
        style="margin-top: 20px"
      >
        <el-form-item prop="userAccount">
          <el-input
            v-model="dispatchForm.userAccount"
            placeholder="请输入用户账号"
            clearable
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelDispatch">取消</el-button>
          <el-button type="primary" @click="handleConfirmDispatch">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 二次确认弹窗 -->
    <el-dialog
      v-model="confirmDispatchVisible"
      title="确认派发"
      width="300px"
      :close-on-click-modal="false"
    >
      <div class="confirm-content">
        <p>确认将优惠券派发给用户吗？</p>
        <div class="confirm-info">
          <div>优惠券：{{ currentCoupon.couponName }}</div>
          <div>优惠券类型：{{ currentCoupon.type }}</div>
          <div>优惠门槛与金额：{{ currentCoupon.discountRule }}</div>
          <div>用户账号：{{ dispatchForm.userAccount }}</div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelConfirm">取消</el-button>
          <el-button type="primary" @click="handleFinalConfirm">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.coupon-management {
  .common {
    padding: 20px;
    background-color: #fff;
    margin-bottom: 20px;
  }

  .search-section {
    .search-form {
      :deep(.plus-search) {
        background-color: #fff;
      }
    }
  }

  .tab-button-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
  }

  .coupon-tabs {
    flex: 1;

    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }

    :deep(.el-tabs__item) {
      font-size: 14px;
      font-weight: 500;
      color: #606266;

      &.is-active {
        color: #409eff;
        font-weight: 600;
      }

      &:hover {
        color: #409eff;
      }
    }
  }

  .button-section {
    display: flex;
    align-items: center;
    padding-top: 8px; // 微调对齐
  }

  .table-section {
    .operation-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .el-button {
        margin: 0;
        padding: 4px 8px;
      }
    }
  }

  // 手动派发弹窗样式
  :deep(.el-dialog) {
    .dispatch-info {
      .info-item {
        display: flex;
        margin-bottom: 12px;
        font-size: 14px;

        .label {
          color: #606266;
          width: 120px;
          flex-shrink: 0;
        }

        .value {
          color: #303133;
          font-weight: 500;
        }
      }
    }

    .confirm-content {
      text-align: center;

      p {
        margin-bottom: 16px;
        font-size: 14px;
        color: #606266;
      }

      .confirm-info {
        padding: 12px;
        font-size: 14px;

        div {
          margin-bottom: 4px;
          color: #303133;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .dialog-footer {
      text-align: right;
    }
  }
}
</style>
