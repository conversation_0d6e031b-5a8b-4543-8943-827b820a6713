<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { couponSave } from "@/api/coupon";

defineOptions({
  name: "CouponManagementCreate"
});

const router = useRouter();

// 表单数据 - 修改为与API匹配的字段名
const formData = ref({
  name: "", // 优惠券名称
  couponDiscountType: "FULL_REDUCTION", // 优惠券类型，默认选中满减券
  conditionAmount: 0, // 满减条件金额
  discountAmount: 0, // 优惠金额
  totalIssue: 1, // 总发行量
  distributionStartTime: "", // 发放开始时间
  distributionEndTime: "", // 发放结束时间
  isUseLimit: true, // 使用是否有限制
  startTime: "", // 使用开始时间
  endTime: "", // 使用结束时间
  remarks: "", // 备注
  enabled: true // 状态开关
});

// 表单验证规则 - 修改为与API匹配的字段名
const formRules = reactive({
  name: [
    { required: true, message: "请输入优惠券名称", trigger: "blur" },
    { max: 10, message: "优惠券名称不能超过10个字符", trigger: "blur" }
  ],
  couponDiscountType: [
    { required: true, message: "请选择优惠券类型", trigger: "change" }
  ],
  conditionAmount: [
    { required: true, message: "请输入满减条件金额", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value === null || value === undefined || value === "") {
          callback(new Error("请输入满减条件金额"));
        } else if (value <= 0) {
          callback(new Error("满减条件金额必须大于0"));
        } else if (value > 1000000) {
          callback(new Error("满减条件金额不能超过1,000,000"));
        } else if (
          formData.value.discountAmount > 0 &&
          value < formData.value.discountAmount
        ) {
          callback(new Error("满减条件金额必须不小于优惠金额"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  discountAmount: [
    { required: true, message: "请输入优惠金额", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value === null || value === undefined || value === "") {
          callback(new Error("请输入优惠金额"));
        } else if (value <= 0) {
          callback(new Error("优惠金额必须大于0"));
        } else if (value > 1000000) {
          callback(new Error("优惠金额不能超过1,000,000"));
        } else if (
          formData.value.conditionAmount > 0 &&
          value >= formData.value.conditionAmount
        ) {
          callback(new Error("优惠金额不能大于或等于满减条件金额"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  totalIssue: [
    { required: true, message: "请输入总发行量", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value < 1) {
          callback(new Error("总发行量不能小于1"));
        } else if (value > 1000000) {
          callback(new Error("总发行量不能超过1,000,000"));
        } else if (!Number.isInteger(value)) {
          callback(new Error("总发行量必须为整数"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  distributionStartTime: [
    // 移除必填验证
    {
      validator: (rule, value, callback) => {
        if (value && formData.value.distributionEndTime) {
          if (new Date(value) >= new Date(formData.value.distributionEndTime)) {
            callback(new Error("发放开始时间必须早于发放结束时间"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
  distributionEndTime: [
    { required: true, message: "请选择发放结束时间", trigger: "change" },
    {
      validator: (rule, value, callback) => {
        if (value && formData.value.distributionStartTime) {
          if (
            new Date(value) <= new Date(formData.value.distributionStartTime)
          ) {
            callback(new Error("发放结束时间必须晚于发放开始时间"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
  startTime: [
    {
      validator: (rule, value, callback) => {
        // 移除必填验证
        if (value && formData.value.distributionStartTime) {
          if (
            new Date(value) < new Date(formData.value.distributionStartTime)
          ) {
            callback(new Error("使用开始时间不能早于发放开始时间"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
  endTime: [
    {
      validator: (rule, value, callback) => {
        if (formData.value.isUseLimit && !value) {
          callback(new Error("请选择使用结束时间"));
        } else if (value) {
          // 验证使用结束时间必须晚于使用开始时间
          if (
            formData.value.startTime &&
            new Date(value) <= new Date(formData.value.startTime)
          ) {
            callback(new Error("使用结束时间必须晚于使用开始时间"));
          }
          // 验证使用结束时间不能早于发放结束时间
          else if (
            formData.value.distributionEndTime &&
            new Date(value) < new Date(formData.value.distributionEndTime)
          ) {
            callback(new Error("使用结束时间不能早于发放结束时间"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
  remarks: [{ max: 200, message: "备注不能超过200个字符", trigger: "blur" }]
});
//优惠券类型选项
const couponDiscountTypeOptions = [
  { label: "满减券", value: "FULL_REDUCTION" }
];
// 使用时间类型选项
const useTimeOptions = [
  { label: "不限", value: false },
  { label: "有限", value: true }
];

// 状态选项
const statusOptions = [
  { label: "启用", value: true },
  { label: "停用", value: false }
];

const formRef = ref();
const loading = ref(false);

// 时间转换为时间戳的工具函数
const convertToTimestamp = dateValue => {
  if (!dateValue) return 0;
  return new Date(dateValue).getTime();
};

// 获取当前时间戳
const getCurrentTimestamp = () => {
  return new Date().getTime();
};

// 提交表单
const handleSubmit = () => {
  formRef.value.validate(async valid => {
    if (valid) {
      // 时间验证已移至表单验证规则中，此处不再需要额外验证

      loading.value = true;

      try {
        // 构建API请求数据
        const requestData = {
          name: formData.value.name,
          couponDiscountType: formData.value.couponDiscountType, // 传递选中的优惠券类型
          conditionAmount: formData.value.conditionAmount,
          discountAmount: formData.value.discountAmount,
          totalIssue: formData.value.totalIssue,
          // 如果发放开始时间为空，则使用当前时间戳
          distributionStartTime: formData.value.distributionStartTime
            ? convertToTimestamp(formData.value.distributionStartTime)
            : getCurrentTimestamp(),
          distributionEndTime: convertToTimestamp(
            formData.value.distributionEndTime
          ),
          isUseLimit: formData.value.isUseLimit,
          remarks: formData.value.remarks || "",
          couponScope: "ALL", // 固定传"ALL"
          enabled: formData.value.enabled
        };

        // 只有当isUseLimit为true时才传递使用时间
        if (formData.value.isUseLimit) {
          // 如果使用开始时间为空，则使用当前时间戳
          requestData.startTime = formData.value.startTime
            ? convertToTimestamp(formData.value.startTime)
            : getCurrentTimestamp();
          requestData.endTime = convertToTimestamp(formData.value.endTime);
        }

        console.log("提交的API数据:", requestData);

        // 调用API
        const response = await couponSave(requestData);

        ElMessage.success("优惠券创建成功");
        router.push("/coupon/management/index");
      } catch (error) {
        console.error("创建优惠券失败:", error);
        ElMessage.error("创建优惠券失败，请重试");
      } finally {
        loading.value = false;
      }
    } else {
      ElMessage.error("请完善表单信息");
    }
  });
};

// 处理满减条件金额变化
const handleConditionAmountChange = () => {
  // 触发优惠金额的验证
  if (formRef.value) {
    formRef.value.validateField("discountAmount");
  }
};

// 处理优惠金额变化
const handleDiscountAmountChange = () => {
  // 触发满减条件金额的验证
  if (formRef.value) {
    formRef.value.validateField("conditionAmount");
  }
};

// 取消操作
const handleCancel = () => {
  ElMessageBox.confirm(
    "确定要取消新建优惠券吗？未保存的数据将丢失。",
    "确认取消",
    {
      confirmButtonText: "确定",
      cancelButtonText: "继续编辑",
      type: "warning"
    }
  )
    .then(() => {
      router.push("/coupon/management/index");
    })
    .catch(() => {
      // 用户选择继续编辑
    });
};

onMounted(() => {
  // 页面初始化逻辑
});
</script>

<template>
  <div class="coupon-create">
    <div class="common">
      <div class="section-title">优惠券基础信息</div>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="coupon-form"
      >
        <div class="form-section">
          <el-form-item label="优惠券名称" prop="name" style="margin-bottom: 30px;">
            <el-input
              v-model="formData.name"
              maxlength="10"
              show-word-limit
              placeholder="请输入优惠券名称"
              style="width: 400px"
            />
          </el-form-item>

          <el-form-item label="优惠券类型" prop="couponDiscountType" style="margin-bottom: 30px;">
            <el-radio-group v-model="formData.couponDiscountType">
              <el-radio
                v-for="option in couponDiscountTypeOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="满减金额" prop="conditionAmount" style="margin-bottom: 30px;">
            <div class="discount-input-group">
              <span class="form-text">满</span>
              <el-input
                v-model.number="formData.conditionAmount"
                type="number"
                oninput="value=value.replace(/[^\d]/g,''); if(value > 1000000) value = 1000000"
                placeholder="请输入满减条件金额"
                style="width: 180px"
                @change="handleConditionAmountChange"
              />
              <span class="form-text">减</span>
              <el-input
                v-model.number="formData.discountAmount"
                type="number"
                oninput="value=value.replace(/[^\d]/g,''); if(value > 1000000) value = 1000000"
                placeholder="请输入优惠金额"
                style="width: 180px"
                @change="handleDiscountAmountChange"
              />
            </div>
          </el-form-item>

          <!-- 隐藏的表单项用于验证优惠金额 -->
          <el-form-item prop="discountAmount" style="display: none; margin-bottom: 30px;">
            <el-input v-model="formData.discountAmount" />
          </el-form-item>

          <el-form-item label="总发行量" prop="totalIssue" style="margin-bottom: 30px;">
            <el-input
              v-model.number="formData.totalIssue"
              type="number"
              oninput="value=value.replace(/[^\d]/g,''); if(value > 1000000) value = 1000000"
              placeholder="请输入优惠券总发行量"
              style="width: 400px"
            />
          </el-form-item>

          <el-form-item label="发放时间" prop="distributionStartTime" style="margin-bottom: 30px;">
            <div class="time-range">
              <span>开始</span>
              <el-date-picker
                v-model="formData.distributionStartTime"
                placeholder="请选择开始时间"
                style="width: 200px; margin: 0 10px"
              />
              <span>结束</span>
              <el-date-picker
                v-model="formData.distributionEndTime"
                placeholder="请选择结束时间"
                style="width: 200px; margin-left: 10px"
              />
            </div>
          </el-form-item>

          <el-form-item label="使用时间" prop="isUseLimit" style="margin-bottom: 20px;">
            <el-radio-group v-model="formData.isUseLimit">
              <el-radio
                v-for="option in useTimeOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="formData.isUseLimit" label=" " prop="startTime" style="margin-bottom: 30px;">
            <div class="time-range">
              <span>开始</span>
              <el-date-picker
                v-model="formData.startTime"
                placeholder="请选择开始时间"
                style="width: 200px; margin: 0 10px"
              />
              <span>结束</span>
              <el-date-picker
                v-model="formData.endTime"
                placeholder="请选择结束时间"
                style="width: 200px; margin-left: 10px"
              />
            </div>
          </el-form-item>

          <el-form-item label="备注" prop="remarks" style="margin-bottom: 30px;">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="4"
              maxlength="200"
              show-word-limit
              placeholder="请输入备注信息"
              style="width: 400px"
            />
          </el-form-item>

          <el-form-item label="状态" prop="enabled" style="margin-bottom: 20px;" required>
            <el-radio-group v-model="formData.enabled">
              <el-radio
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>

        <div class="form-actions">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            确认
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.coupon-create {
  .common {
    height: 88vh;
    padding: 20px;
    background-color: #fff;
    position: relative;
  }

  .page-header {
    margin-bottom: 24px;

    h2 {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
      margin: 0;
    }
  }

  .coupon-form {
    max-width: 800px; /* 设置一个最大宽度 */
    margin: 0 auto; /* 居中显示 */
    padding-bottom: 80px;
  }

  .form-section {
    margin-bottom: 32px;
  }

  .section-title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    margin: 20px 0 20px 10px;

    &::after {
      content: "";
      flex: 1;
      height: 1px;
      background: #e4e7ed;
      margin-left: 16px;
    }
  }

  .time-range {
    display: flex;
    align-items: center;

    span {
      color: #606266;
      font-size: 14px;
    }
  }

  .discount-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .form-text {
    margin: 0 10px;
    color: #606266;
    font-size: 14px;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    position: absolute;
    bottom: 20px;
    right: 20px;
    left: 20px;
    background-color: #fff;
    padding-top: 20px;
  }
}
</style>
